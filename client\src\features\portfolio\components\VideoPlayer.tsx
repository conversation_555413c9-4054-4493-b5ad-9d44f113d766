/**
 * Video player component with intersection observer for autoplay
 */

import React, { useRef, useState, useEffect } from 'react';
import { useErrorHandler } from '@/components/ErrorBoundary';

interface VideoPlayerProps {
  src: string;
  poster?: string;
  className?: string;
  onLoadStart?: () => void;
  onLoadedData?: () => void;
  onError?: (error: React.SyntheticEvent<HTMLVideoElement, Event>) => void;
  onClick?: () => void;
  autoplay?: boolean;
  threshold?: number;
}

export default function VideoPlayer({
  src,
  poster,
  className = '',
  onLoadStart,
  onLoadedData,
  onError,
  onClick,
  autoplay = true,
  threshold = 0.5
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isInView, setIsInView] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const { handleError } = useErrorHandler();

  // Intersection Observer for autoplay/pause
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !autoplay) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsInView(entry.isIntersecting);

        if (entry.isIntersecting && entry.intersectionRatio > threshold) {
          // Video is more than threshold% visible, start playing
          video.play().catch((error) => {
            console.warn('Video autoplay failed:', error);
          });
        } else {
          // Video is not visible or less than threshold% visible, pause
          video.pause();
        }
      },
      {
        threshold: [0, threshold, 1],
        rootMargin: '0px 0px -10% 0px' // Start playing slightly before fully visible
      }
    );

    observer.observe(video);

    return () => {
      observer.disconnect();
      video.pause();
    };
  }, [autoplay, threshold]);

  const handleLoadStart = () => {
    setIsLoading(true);
    setHasError(false);
    onLoadStart?.();
  };

  const handleLoadedData = () => {
    setIsLoading(false);
    onLoadedData?.();
  };

  const handleVideoError = (error: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    try {
      setIsLoading(false);
      setHasError(true);
      onError?.(error);
    } catch (err) {
      handleError(err as Error);
    }
  };

  const handleClick = () => {
    try {
      onClick?.();
    } catch (error) {
      handleError(error as Error);
    }
  };

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-[#1A1A1A] z-10">
          <div className="w-8 h-8 border-2 border-[#FF3366] border-t-transparent rounded-full animate-spin" />
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-[#1A1A1A] z-10">
          <div className="text-center">
            <div className="text-red-400 mb-2">⚠️</div>
            <p className="text-gray-400 text-sm">Video unavailable</p>
          </div>
        </div>
      )}

      {/* Click overlay for modal trigger */}
      {onClick && (
        <div
          className="absolute inset-0 z-20 cursor-pointer group"
          onClick={handleClick}
          role="button"
          tabIndex={0}
          aria-label="Open video in fullscreen"
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleClick();
            }
          }}
        >
          {/* Play button overlay */}
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="w-16 h-16 bg-black bg-opacity-50 rounded-full flex items-center justify-center backdrop-blur-sm">
              <div className="w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1" />
            </div>
          </div>
        </div>
      )}

      {/* Video element */}
      <video
        ref={videoRef}
        src={src}
        poster={poster}
        className={`w-full h-full object-cover transition-opacity duration-500 ${
          isLoading || hasError ? 'opacity-0' : 'opacity-100'
        } ${className}`}
        muted
        loop
        playsInline
        preload="metadata"
        onLoadStart={handleLoadStart}
        onLoadedData={handleLoadedData}
        onError={handleVideoError}
        style={{
          willChange: isInView ? 'auto' : 'transform'
        }}
      />

      {/* Video overlay gradient */}
      <div className="absolute inset-0 bg-gradient-to-t from-[#0F0F0F] via-transparent to-transparent opacity-60 pointer-events-none" />
    </div>
  );
}
