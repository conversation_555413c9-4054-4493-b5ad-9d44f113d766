/**
 * Refactored About section component using smaller, focused components
 */

import React, { useRef, useCallback, useState } from 'react';
import { useScrollTrigger } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { Z_INDEX } from '@/constants';
import ScannerAnimation from './ScannerAnimation';
import AIBrainOutline from './AIBrainOutline';
import AnimatedContent from './AnimatedContent';
import PersonalInfo from './PersonalInfo';
import TechnicalSkills from './TechnicalSkills';

interface AboutSectionProps {
  className?: string;
  id?: string;
}

export default function AboutSection({
  className = '',
  id = 'about'
}: AboutSectionProps) {
  const sectionRef = useRef<HTMLElement>(null);
  const [animationPhase, setAnimationPhase] = useState<'idle' | 'scanner' | 'brain' | 'content' | 'complete'>('idle');
  
  const { createScrollTrigger } = useScrollTrigger();
  const { handleError } = useErrorHandler();

  const handleScannerComplete = useCallback(() => {
    setAnimationPhase('brain');
  }, []);

  const handleBrainComplete = useCallback(() => {
    setAnimationPhase('content');
  }, []);

  const handleContentComplete = useCallback(() => {
    setAnimationPhase('complete');
  }, []);

  const initializeAnimations = useCallback(() => {
    try {
      setAnimationPhase('scanner');
    } catch (error) {
      handleError(error as Error);
    }
  }, [handleError]);

  // Set up scroll trigger
  React.useEffect(() => {
    if (!sectionRef.current) return;

    const trigger = createScrollTrigger({
      trigger: sectionRef.current,
      start: "top 70%",
      once: true,
      onEnter: initializeAnimations,
    });

    return () => {
      if (trigger) trigger.kill();
    };
  }, [createScrollTrigger, initializeAnimations]);

  return (
    <section
      ref={sectionRef}
      className={`min-h-screen bg-[#0F0F0F] py-20 px-4 relative overflow-hidden ${className}`}
      id={id}
      role="region"
      aria-labelledby="about-heading"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#1A1A1A] via-[#0F0F0F] to-[#1A1A1A] opacity-80" />
      
      {/* Scanner Animation */}
      <ScannerAnimation
        autoStart={animationPhase === 'scanner'}
        startDelay={0}
        onComplete={handleScannerComplete}
      />

      {/* AI Brain Outline */}
      <AIBrainOutline
        autoStart={animationPhase === 'brain'}
        startDelay={0}
        onComplete={handleBrainComplete}
        intensity="normal"
      />
      
      <div className="max-w-4xl mx-auto relative" style={{ zIndex: Z_INDEX.CONTENT }}>
        {/* Section Title */}
        <AnimatedContent
          autoStart={animationPhase === 'content'}
          startDelay={0}
          staggerDelay={0.12}
          animationType="fadeUp"
          className="text-center mb-16"
        >
          <h2
            data-animate
            id="about-heading"
            className="text-4xl md:text-5xl lg:text-6xl font-black text-[#F5F5F5] mb-6 tracking-wide"
          >
            ABOUT ME
          </h2>
        </AnimatedContent>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Left Column - Personal Info */}
          <AnimatedContent
            autoStart={animationPhase === 'content'}
            startDelay={300}
            staggerDelay={0.15}
            animationType="slideLeft"
          >
            <PersonalInfo />
          </AnimatedContent>

          {/* Right Column - Technical Skills */}
          <AnimatedContent
            autoStart={animationPhase === 'content'}
            startDelay={600}
            staggerDelay={0.15}
            animationType="slideRight"
            onComplete={handleContentComplete}
          >
            <TechnicalSkills />
          </AnimatedContent>
        </div>
      </div>
    </section>
  );
}

// Export sub-components for potential reuse
export {
  ScannerAnimation,
  AIBrainOutline,
  AnimatedContent,
  PersonalInfo,
  TechnicalSkills
};
