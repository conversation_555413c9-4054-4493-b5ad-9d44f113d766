/**
 * UFO-style spaceship SVG component using the new base system
 */

import React from 'react';
import { BaseSpaceship, DEFAULT_UFO_CONFIG, AnimatedSpaceshipWrapper } from '@/components/spaceships';
import type { AnimatedSpaceshipProps } from '@/components/spaceships';
import { COLOR_VARIANTS } from '@/constants';

interface UFOSpaceshipProps extends AnimatedSpaceshipProps {
  enableBeam?: boolean;
  enableLights?: boolean;
}

export default function UFOSpaceship({
  className = '',
  size = 'md',
  enableHover = true,
  enableFloating = false,
  enableBeam = false,
  enableLights = false,
  onHover,
  onClick,
  ...props
}: UFOSpaceshipProps) {

  return (
    <BaseSpaceship
      className={className}
      size={size}
      enableHover={enableHover}
      enableFloating={enableFloating}
      onHover={onHover}
      onClick={onClick}
      config={DEFAULT_UFO_CONFIG}
      {...props}
    >
      {({ refs, sizeConfig, animationContext }) => (
        <AnimatedSpaceshipWrapper
          enableBeam={enableBeam}
          enableGlow={enableHover}
          enableLights={enableLights}
          beamRef={refs.beam}
          glowRef={refs.glow}
          lightsRef={refs.lights}
          isActive={animationContext.isHovering || animationContext.isFloating}
          config={{
            beam: { type: 'trace' },
            glow: { intensity: 1, color: COLOR_VARIANTS.BLUE },
            lights: { pattern: 'blink' }
          }}
        >
          <svg
            ref={refs.svg}
            width={sizeConfig.width}
            height={sizeConfig.height}
            viewBox={sizeConfig.viewBox}
            className="overflow-visible"
            style={{ transformOrigin: 'center center' }}
          >
            {/* Glow Effect */}
            <defs>
              <radialGradient id="ufo-glow" cx="50%" cy="50%" r="70%">
                <stop offset="0%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.6" />
                <stop offset="50%" stopColor={COLOR_VARIANTS.PINK} stopOpacity="0.3" />
                <stop offset="100%" stopColor="transparent" stopOpacity="0" />
              </radialGradient>

              <linearGradient id="ufo-body" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#E8E8E8" />
                <stop offset="50%" stopColor={COLOR_VARIANTS.WHITE} />
                <stop offset="100%" stopColor="#D0D0D0" />
              </linearGradient>

              <linearGradient id="ufo-dome" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.8" />
                <stop offset="50%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.4" />
                <stop offset="100%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.2" />
              </linearGradient>

              <linearGradient id="tractor-beam" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.6" />
                <stop offset="50%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.3" />
                <stop offset="100%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.1" />
              </linearGradient>
            </defs>

            {/* Glow Background */}
            <g ref={refs.glow} opacity="0.4">
              <ellipse
                cx={sizeConfig.width / 2}
                cy={sizeConfig.height * 0.4}
                rx={sizeConfig.width * 0.6}
                ry={sizeConfig.height * 0.3}
                fill="url(#ufo-glow)"
              />
            </g>

            {/* Tractor Beam */}
            <g ref={refs.beam} opacity="0.3" style={{ transformOrigin: `${sizeConfig.width / 2}px ${sizeConfig.height * 0.5}px` }}>
              <polygon
                points={`${sizeConfig.width * 0.4},${sizeConfig.height * 0.5} ${sizeConfig.width * 0.6},${sizeConfig.height * 0.5} ${sizeConfig.width * 0.8},${sizeConfig.height * 0.9} ${sizeConfig.width * 0.2},${sizeConfig.height * 0.9}`}
                fill="url(#tractor-beam)"
              />
            </g>

            {/* UFO Body */}
            <g>
              {/* Main Saucer */}
              <ellipse
                cx={sizeConfig.width / 2}
                cy={sizeConfig.height * 0.45}
                rx={sizeConfig.width * 0.35}
                ry={sizeConfig.height * 0.15}
                fill="url(#ufo-body)"
                stroke={COLOR_VARIANTS.BLUE}
                strokeWidth="1"
              />

              {/* Dome */}
              <ellipse
                cx={sizeConfig.width / 2}
                cy={sizeConfig.height * 0.35}
                rx={sizeConfig.width * 0.2}
                ry={sizeConfig.height * 0.15}
                fill="url(#ufo-dome)"
                stroke={COLOR_VARIANTS.BLUE}
                strokeWidth="1"
              />

              {/* Center Ring */}
              <ellipse
                cx={sizeConfig.width / 2}
                cy={sizeConfig.height * 0.45}
                rx={sizeConfig.width * 0.25}
                ry={sizeConfig.height * 0.08}
                fill="none"
                stroke={COLOR_VARIANTS.BLUE}
                strokeWidth="1.5"
              />
            </g>

            {/* Static Lights */}
            <g ref={refs.lights}>
              <circle
                cx={sizeConfig.width * 0.25}
                cy={sizeConfig.height * 0.45}
                r="2"
                fill={COLOR_VARIANTS.PINK}
                opacity="0.6"
              />
              <circle
                cx={sizeConfig.width * 0.4}
                cy={sizeConfig.height * 0.42}
                r="2"
                fill={COLOR_VARIANTS.BLUE}
                opacity="0.6"
              />
              <circle
                cx={sizeConfig.width * 0.6}
                cy={sizeConfig.height * 0.42}
                r="2"
                fill={COLOR_VARIANTS.PINK}
                opacity="0.6"
              />
              <circle
                cx={sizeConfig.width * 0.75}
                cy={sizeConfig.height * 0.45}
                r="2"
                fill={COLOR_VARIANTS.BLUE}
                opacity="0.6"
              />
            </g>
          </svg>
        </AnimatedSpaceshipWrapper>
      )}
    </BaseSpaceship>
  );
}
