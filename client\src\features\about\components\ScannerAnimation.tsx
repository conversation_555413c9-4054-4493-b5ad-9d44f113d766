/**
 * Robot eye scanner animation component
 */

import React, { useRef, useEffect } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { ANIMATION_EASINGS, Z_INDEX } from '@/constants';

interface ScannerAnimationProps {
  autoStart?: boolean;
  startDelay?: number;
  onComplete?: () => void;
  className?: string;
}

export default function ScannerAnimation({
  autoStart = false,
  startDelay = 0,
  onComplete,
  className = ''
}: ScannerAnimationProps) {
  const scanLineRef = useRef<HTMLDivElement>(null);
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  const startAnimation = React.useCallback(() => {
    try {
      if (!gsap || !isAvailable || !scanLineRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) {
        // Skip animation for reduced motion
        onComplete?.();
        return;
      }

      const timeline = gsap.timeline({
        onComplete: () => {
          // Remove will-change after animation
          if (scanLineRef.current) {
            scanLineRef.current.style.willChange = 'auto';
          }
          onComplete?.();
        }
      });

      // Phase 1: Robot Eye Scanner Animation (1.8 seconds total)
      timeline
        .set(scanLineRef.current, {
          opacity: 1,
          translateX: '-100%',
          scaleX: 0.1,
          willChange: 'transform'
        })
        // Scanner sweep across screen
        .to(scanLineRef.current, {
          translateX: '100vw',
          scaleX: 1,
          duration: 1.5,
          ease: 'power2.inOut',
          boxShadow: '0 0 30px #4A90E2, 0 0 60px #4A90E2, 0 0 90px #4A90E2'
        }, 0)
        // Scanner fade out
        .to(scanLineRef.current, {
          opacity: 0,
          duration: 0.3,
          ease: ANIMATION_EASINGS.POWER2_OUT
        }, 1.5);

    } catch (error) {
      handleError(error as Error);
    }
  }, [gsap, isAvailable, onComplete, handleError]);

  // Auto-start effect
  useEffect(() => {
    if (autoStart && isAvailable) {
      const timer = setTimeout(startAnimation, startDelay);
      return () => clearTimeout(timer);
    }
  }, [autoStart, isAvailable, startAnimation, startDelay]);

  // Initialize scanner as hidden
  useEffect(() => {
    if (isAvailable && scanLineRef.current && gsap) {
      gsap.set(scanLineRef.current, { opacity: 0 });
    }
  }, [isAvailable, gsap]);

  return (
    <div
      ref={scanLineRef}
      className={`fixed top-0 left-0 w-1 h-full opacity-0 pointer-events-none ${className}`}
      style={{
        background: 'linear-gradient(to bottom, transparent 0%, #4A90E2 20%, #4A90E2 80%, transparent 100%)',
        filter: 'blur(1px)',
        zIndex: Z_INDEX.CONTENT + 5,
        transform: 'translateX(-100%)',
        willChange: 'transform'
      }}
    />
  );
}

// Export the start animation function for external control
export { ScannerAnimation };
