/**
 * Contact Feature Hooks Tests
 * Tests for contact-related custom hooks
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useContactForm, useContactAnimations } from '@/features/contact/hooks';

// Mock dependencies
vi.mock('@/hooks/useGSAP', () => ({
  useGSAP: () => ({
    gsap: {
      timeline: vi.fn(() => ({
        set: vi.fn().mockReturnThis(),
        to: vi.fn().mockReturnThis(),
        fromTo: vi.fn().mockReturnThis(),
        kill: vi.fn(),
      })),
      set: vi.fn(),
      to: vi.fn(),
      fromTo: vi.fn(),
    },
    isAvailable: true,
  }),
}));

vi.mock('@/utils', () => ({
  getAccessibilityConfig: () => ({
    reduceMotion: false,
    highContrast: false,
  }),
}));

// Mock the validation and email services
vi.mock('@/features/contact/utils/contactValidation', () => ({
  contactValidation: {
    validateContactForm: vi.fn(() => ({
      isValid: true,
      errors: {},
    })),
  },
}));

vi.mock('@/features/contact/utils/emailService', () => ({
  emailService: {
    sendContactEmail: vi.fn(() => Promise.resolve({
      success: true,
      message: 'Email sent successfully',
    })),
  },
}));

describe('useContactForm Hook', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useContactForm());

    expect(result.current.data).toEqual({
      name: '',
      email: '',
      message: '',
      subject: '',
      phone: '',
      company: '',
    });
    expect(result.current.errors).toEqual({});
    expect(result.current.isSubmitting).toBe(false);
    expect(result.current.isSubmitted).toBe(false);
    expect(result.current.submitError).toBeNull();
    expect(result.current.submitSuccess).toBe(false);
  });

  it('should update field values', () => {
    const { result } = renderHook(() => useContactForm());

    act(() => {
      result.current.updateField('name', 'John Doe');
    });

    expect(result.current.data.name).toBe('John Doe');
  });

  it('should clear error when updating field', () => {
    const { result } = renderHook(() => useContactForm());

    // First set an error
    act(() => {
      result.current.setError('name', 'Name is required');
    });

    expect(result.current.errors.name).toBe('Name is required');

    // Then update the field
    act(() => {
      result.current.updateField('name', 'John Doe');
    });

    expect(result.current.errors.name).toBeUndefined();
  });

  it('should set and clear errors', () => {
    const { result } = renderHook(() => useContactForm());

    act(() => {
      result.current.setError('email', 'Invalid email');
    });

    expect(result.current.errors.email).toBe('Invalid email');

    act(() => {
      result.current.clearError('email');
    });

    expect(result.current.errors.email).toBeUndefined();
  });

  it('should clear all errors', () => {
    const { result } = renderHook(() => useContactForm());

    act(() => {
      result.current.setError('name', 'Name error');
      result.current.setError('email', 'Email error');
    });

    expect(Object.keys(result.current.errors)).toHaveLength(2);

    act(() => {
      result.current.clearAllErrors();
    });

    expect(result.current.errors).toEqual({});
  });

  it('should handle form submission successfully', async () => {
    const { result } = renderHook(() => useContactForm());

    // Fill out form
    act(() => {
      result.current.updateField('name', 'John Doe');
      result.current.updateField('email', '<EMAIL>');
      result.current.updateField('message', 'Test message');
    });

    // Submit form
    await act(async () => {
      await result.current.submitForm();
    });

    expect(result.current.submitSuccess).toBe(true);
    expect(result.current.isSubmitted).toBe(true);
    expect(result.current.submitError).toBeNull();
  });

  it('should handle form submission with custom callback', async () => {
    const mockCallback = vi.fn(() => Promise.resolve());
    const { result } = renderHook(() => useContactForm(mockCallback));

    // Fill out form
    act(() => {
      result.current.updateField('name', 'John Doe');
      result.current.updateField('email', '<EMAIL>');
      result.current.updateField('message', 'Test message');
    });

    // Submit form
    await act(async () => {
      await result.current.submitForm();
    });

    expect(mockCallback).toHaveBeenCalledWith({
      name: 'John Doe',
      email: '<EMAIL>',
      message: 'Test message',
      subject: '',
      phone: '',
      company: '',
    });
  });

  it('should reset form', () => {
    const { result } = renderHook(() => useContactForm());

    // Fill out form and set some state
    act(() => {
      result.current.updateField('name', 'John Doe');
      result.current.setError('email', 'Error');
      result.current.setSubmitting(true);
    });

    // Reset form
    act(() => {
      result.current.resetForm();
    });

    expect(result.current.data.name).toBe('');
    expect(result.current.errors).toEqual({});
    expect(result.current.isSubmitting).toBe(false);
    expect(result.current.isSubmitted).toBe(false);
    expect(result.current.submitError).toBeNull();
    expect(result.current.submitSuccess).toBe(false);
  });
});

describe('useContactAnimations Hook', () => {
  let mockRef: React.RefObject<HTMLElement>;

  beforeEach(() => {
    mockRef = {
      current: document.createElement('div'),
    };
  });

  it('should initialize with animation functions', () => {
    const { result } = renderHook(() => useContactAnimations());

    expect(typeof result.current.animateContactSection).toBe('function');
    expect(typeof result.current.animateSpaceshipFlyIn).toBe('function');
    expect(typeof result.current.animateTextReveal).toBe('function');
    expect(typeof result.current.animateFormSubmission).toBe('function');
    expect(typeof result.current.animateParticleExplosion).toBe('function');
    expect(typeof result.current.cleanup).toBe('function');
    expect(result.current.isAvailable).toBe(true);
  });

  it('should animate contact section', () => {
    const { result } = renderHook(() => useContactAnimations());

    // Add some mock elements
    const element = document.createElement('div');
    element.setAttribute('data-animate', '');
    mockRef.current?.appendChild(element);

    act(() => {
      result.current.animateContactSection(mockRef);
    });

    // Should not throw
    expect(result.current.animateContactSection).toBeDefined();
  });

  it('should animate spaceship fly-in', () => {
    const { result } = renderHook(() => useContactAnimations());

    act(() => {
      result.current.animateSpaceshipFlyIn(mockRef, {
        direction: 'left',
        delay: 0.5,
        duration: 1.5,
      });
    });

    // Should not throw
    expect(result.current.animateSpaceshipFlyIn).toBeDefined();
  });

  it('should animate text reveal', () => {
    const { result } = renderHook(() => useContactAnimations());

    act(() => {
      result.current.animateTextReveal(mockRef, false);
    });

    act(() => {
      result.current.animateTextReveal(mockRef, true);
    });

    // Should not throw
    expect(result.current.animateTextReveal).toBeDefined();
  });

  it('should animate form submission', () => {
    const { result } = renderHook(() => useContactAnimations());

    // Add a submit button
    const button = document.createElement('button');
    button.type = 'submit';
    mockRef.current?.appendChild(button);

    act(() => {
      result.current.animateFormSubmission(mockRef, true);
    });

    act(() => {
      result.current.animateFormSubmission(mockRef, false);
    });

    // Should not throw
    expect(result.current.animateFormSubmission).toBeDefined();
  });

  it('should animate particle explosion', () => {
    const { result } = renderHook(() => useContactAnimations());

    act(() => {
      result.current.animateParticleExplosion(mockRef, 100, 100);
    });

    // Should not throw
    expect(result.current.animateParticleExplosion).toBeDefined();
  });

  it('should cleanup animations', () => {
    const { result } = renderHook(() => useContactAnimations());

    act(() => {
      result.current.cleanup();
    });

    // Should not throw
    expect(result.current.cleanup).toBeDefined();
  });
});
