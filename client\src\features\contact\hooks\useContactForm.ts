/**
 * Contact Form Hook
 * Centralized state management and logic for contact form functionality
 */

import { useState, useCallback } from 'react';
import { ContactFormData, ContactFormHookReturn, ContactFormErrors } from '../types';
import { contactValidation } from '../utils/contactValidation';
import { emailService } from '../utils/emailService';

const initialFormData: ContactFormData = {
  name: '',
  email: '',
  message: '',
  subject: '',
  phone: '',
  company: '',
};

const initialErrors: ContactFormErrors = {};

export const useContactForm = (
  onSubmitCallback?: (data: ContactFormData) => Promise<void>
): ContactFormHookReturn => {
  const [data, setData] = useState<ContactFormData>(initialFormData);
  const [errors, setErrors] = useState<ContactFormErrors>(initialErrors);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const updateField = useCallback((field: keyof ContactFormData, value: string) => {
    setData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  const setError = useCallback((field: keyof ContactFormErrors, error: string) => {
    setErrors(prev => ({ ...prev, [field]: error }));
  }, []);

  const clearError = useCallback((field: keyof ContactFormErrors) => {
    setErrors(prev => ({ ...prev, [field]: undefined }));
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  const setSubmitting = useCallback((submitting: boolean) => {
    setIsSubmitting(submitting);
  }, []);

  const setSubmitted = useCallback((submitted: boolean) => {
    setIsSubmitted(submitted);
  }, []);

  const setSubmitErrorCallback = useCallback((error: string | null) => {
    setSubmitError(error);
  }, []);

  const setSubmitSuccessCallback = useCallback((success: boolean) => {
    setSubmitSuccess(success);
  }, []);

  const resetForm = useCallback(() => {
    setData(initialFormData);
    setErrors(initialErrors);
    setIsSubmitting(false);
    setIsSubmitted(false);
    setSubmitError(null);
    setSubmitSuccess(false);
  }, []);

  const validateForm = useCallback((): boolean => {
    const validationResult = contactValidation.validateContactForm(data);
    
    if (!validationResult.isValid) {
      setErrors(validationResult.errors);
      return false;
    }
    
    clearAllErrors();
    return true;
  }, [data, clearAllErrors]);

  const submitForm = useCallback(async (): Promise<void> => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    setSubmitError(null);
    setSubmitSuccess(false);

    try {
      // Validate form
      if (!validateForm()) {
        setIsSubmitting(false);
        return;
      }

      // Use custom callback if provided, otherwise use email service
      if (onSubmitCallback) {
        await onSubmitCallback(data);
      } else {
        const result = await emailService.sendContactEmail(data);
        if (!result.success) {
          throw new Error(result.error || 'Failed to send email');
        }
      }

      setSubmitSuccess(true);
      setIsSubmitted(true);
      
      // Reset form after successful submission
      setTimeout(() => {
        resetForm();
      }, 3000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setSubmitError(errorMessage);
      console.error('Contact form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [data, isSubmitting, validateForm, onSubmitCallback, resetForm]);

  return {
    data,
    errors,
    isSubmitting,
    isSubmitted,
    submitError,
    submitSuccess,
    updateField,
    setError,
    clearError,
    clearAllErrors,
    setSubmitting,
    setSubmitted,
    setSubmitError: setSubmitErrorCallback,
    setSubmitSuccess: setSubmitSuccessCallback,
    resetForm,
    submitForm,
  };
};

export default useContactForm;
