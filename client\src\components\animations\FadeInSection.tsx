/**
 * Reusable fade-in section component with staggered child animations
 */

import React, { useRef, useEffect, useCallback } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { ANIMATION_DURATIONS, ANIMATION_EASINGS } from '@/constants';

type FadeDirection = 'up' | 'down' | 'left' | 'right' | 'scale' | 'none';

interface FadeInSectionProps {
  children: React.ReactNode;
  direction?: FadeDirection;
  duration?: number;
  delay?: number;
  stagger?: number;
  distance?: number;
  autoStart?: boolean;
  triggerOnScroll?: boolean;
  triggerStart?: string;
  triggerEnd?: string;
  className?: string;
  onComplete?: () => void;
  onStart?: () => void;
  disabled?: boolean;
  animateChildren?: boolean;
  childSelector?: string;
}

export default function FadeInSection({
  children,
  direction = 'up',
  duration = ANIMATION_DURATIONS.SLOW,
  delay = 0,
  stagger = 0.1,
  distance = 30,
  autoStart = false,
  triggerOnScroll = true,
  triggerStart = 'top 80%',
  triggerEnd = 'bottom 20%',
  className = '',
  onComplete,
  onStart,
  disabled = false,
  animateChildren = false,
  childSelector = '[data-animate]'
}: FadeInSectionProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  // Get animation properties based on direction
  const getAnimationProps = useCallback((dir: FadeDirection) => {
    const props = {
      from: { opacity: 0 } as any,
      to: { opacity: 1 } as any
    };

    switch (dir) {
      case 'up':
        props.from.y = distance;
        props.to.y = 0;
        break;
      case 'down':
        props.from.y = -distance;
        props.to.y = 0;
        break;
      case 'left':
        props.from.x = distance;
        props.to.x = 0;
        break;
      case 'right':
        props.from.x = -distance;
        props.to.x = 0;
        break;
      case 'scale':
        props.from.scale = 0.8;
        props.to.scale = 1;
        break;
      case 'none':
        // Only opacity animation
        break;
    }

    return props;
  }, [distance]);

  // Main animation function
  const startAnimation = useCallback(() => {
    try {
      if (disabled || !isAvailable || !gsap || !containerRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) {
        // Set final state immediately for reduced motion
        gsap.set(containerRef.current, { opacity: 1 });
        if (animateChildren) {
          const children = containerRef.current.querySelectorAll(childSelector);
          gsap.set(children, { opacity: 1 });
        }
        onComplete?.();
        return;
      }

      onStart?.();

      const animProps = getAnimationProps(direction);
      const timeline = gsap.timeline({
        onComplete
      });

      if (animateChildren) {
        // Animate children with stagger
        const childElements = containerRef.current.querySelectorAll(childSelector);
        
        if (childElements.length > 0) {
          // Set initial states for children
          gsap.set(childElements, animProps.from);
          
          // Animate children
          timeline.to(childElements, {
            ...animProps.to,
            duration,
            ease: ANIMATION_EASINGS.POWER2_OUT,
            stagger,
            delay
          });
        } else {
          // Fallback to container animation if no children found
          timeline.fromTo(containerRef.current, animProps.from, {
            ...animProps.to,
            duration,
            ease: ANIMATION_EASINGS.POWER2_OUT,
            delay
          });
        }
      } else {
        // Animate the container itself
        timeline.fromTo(containerRef.current, animProps.from, {
          ...animProps.to,
          duration,
          ease: ANIMATION_EASINGS.POWER2_OUT,
          delay
        });
      }

    } catch (error) {
      handleError(error as Error);
    }
  }, [
    disabled,
    isAvailable,
    gsap,
    direction,
    duration,
    delay,
    stagger,
    animateChildren,
    childSelector,
    getAnimationProps,
    onComplete,
    onStart,
    handleError
  ]);

  // Initialize animation
  useEffect(() => {
    if (!isAvailable || !containerRef.current) return;

    const animProps = getAnimationProps(direction);

    // Hide elements initially
    if (animateChildren) {
      const childElements = containerRef.current.querySelectorAll(childSelector);
      if (childElements.length > 0) {
        gsap?.set(childElements, animProps.from);
      } else {
        gsap?.set(containerRef.current, animProps.from);
      }
    } else {
      gsap?.set(containerRef.current, animProps.from);
    }

    if (autoStart && !triggerOnScroll) {
      const timer = setTimeout(startAnimation, 100);
      return () => clearTimeout(timer);
    }

    if (triggerOnScroll) {
      const trigger = gsap?.timeline().to({}, {
        duration: 0,
        scrollTrigger: {
          trigger: containerRef.current,
          start: triggerStart,
          end: triggerEnd,
          once: true,
          onEnter: startAnimation
        }
      });

      return () => {
        if (trigger) trigger.kill();
      };
    }
  }, [
    isAvailable,
    gsap,
    autoStart,
    triggerOnScroll,
    triggerStart,
    triggerEnd,
    startAnimation,
    animateChildren,
    childSelector,
    getAnimationProps,
    direction
  ]);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
}

// Preset components for common use cases
export const FadeUpSection = (props: Omit<FadeInSectionProps, 'direction'>) => (
  <FadeInSection {...props} direction="up" />
);

export const FadeLeftSection = (props: Omit<FadeInSectionProps, 'direction'>) => (
  <FadeInSection {...props} direction="left" />
);

export const FadeRightSection = (props: Omit<FadeInSectionProps, 'direction'>) => (
  <FadeInSection {...props} direction="right" />
);

export const ScaleInSection = (props: Omit<FadeInSectionProps, 'direction'>) => (
  <FadeInSection {...props} direction="scale" />
);

// Higher-order component for wrapping existing components
export function withFadeIn<T extends object>(
  WrappedComponent: React.ComponentType<T>,
  fadeProps: Partial<FadeInSectionProps> = {}
) {
  return function FadeInWrapper(props: T) {
    return (
      <FadeInSection {...fadeProps}>
        <WrappedComponent {...props} />
      </FadeInSection>
    );
  };
}

// Hook for manual fade-in control
export function useFadeInAnimation(
  direction: FadeDirection = 'up',
  options: {
    duration?: number;
    distance?: number;
  } = {}
) {
  const elementRef = useRef<HTMLElement>(null);
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  const fadeIn = useCallback((customDelay = 0) => {
    try {
      if (!isAvailable || !gsap || !elementRef.current) return;

      const { duration = ANIMATION_DURATIONS.SLOW, distance = 30 } = options;
      
      const animProps = {
        from: { opacity: 0 } as any,
        to: { opacity: 1 } as any
      };

      switch (direction) {
        case 'up':
          animProps.from.y = distance;
          animProps.to.y = 0;
          break;
        case 'down':
          animProps.from.y = -distance;
          animProps.to.y = 0;
          break;
        case 'left':
          animProps.from.x = distance;
          animProps.to.x = 0;
          break;
        case 'right':
          animProps.from.x = -distance;
          animProps.to.x = 0;
          break;
        case 'scale':
          animProps.from.scale = 0.8;
          animProps.to.scale = 1;
          break;
      }

      return gsap.fromTo(elementRef.current, animProps.from, {
        ...animProps.to,
        duration,
        ease: ANIMATION_EASINGS.POWER2_OUT,
        delay: customDelay
      });

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, direction, options, handleError]);

  const fadeOut = useCallback((customDelay = 0) => {
    try {
      if (!isAvailable || !gsap || !elementRef.current) return;

      return gsap.to(elementRef.current, {
        opacity: 0,
        duration: ANIMATION_DURATIONS.FAST,
        ease: ANIMATION_EASINGS.POWER2_IN,
        delay: customDelay
      });

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, handleError]);

  return {
    elementRef,
    fadeIn,
    fadeOut,
    isAvailable
  };
}
