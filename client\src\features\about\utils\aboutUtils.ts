/**
 * About Utility Functions
 * Helper functions for about section data manipulation and processing
 */

import { <PERSON><PERSON>, PersonalInfo, SkillFilter, SkillSortOption } from '../types';

export const aboutUtils = {
  /**
   * Group skills by category
   */
  groupSkillsByCategory: (skills: Skill[]): Record<string, Skill[]> => {
    return skills.reduce((acc, skill) => {
      if (!acc[skill.category]) {
        acc[skill.category] = [];
      }
      acc[skill.category].push(skill);
      return acc;
    }, {} as Record<string, Skill[]>);
  },

  /**
   * Get skill level counts
   */
  getSkillLevelCounts: (skills: Skill[]): Record<string, number> => {
    return skills.reduce((acc, skill) => {
      acc[skill.level] = (acc[skill.level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  },

  /**
   * Filter skills based on criteria
   */
  filterSkills: (skills: Skill[], filter: SkillFilter): Skill[] => {
    return skills.filter(skill => {
      // Category filter
      if (filter.category && filter.category.length > 0) {
        if (!filter.category.includes(skill.category)) {
          return false;
        }
      }

      // Level filter
      if (filter.level && filter.level.length > 0) {
        if (!filter.level.includes(skill.level)) {
          return false;
        }
      }

      // Search query filter
      if (filter.searchQuery) {
        const query = filter.searchQuery.toLowerCase();
        const matchesName = skill.name.toLowerCase().includes(query);
        const matchesDescription = skill.description?.toLowerCase().includes(query);
        
        if (!matchesName && !matchesDescription) {
          return false;
        }
      }

      return true;
    });
  },

  /**
   * Sort skills by specified criteria
   */
  sortSkills: (skills: Skill[], sortOption: SkillSortOption): Skill[] => {
    return [...skills].sort((a, b) => {
      const aValue = a[sortOption.key];
      const bValue = b[sortOption.key];

      // Handle level sorting with custom order
      if (sortOption.key === 'level') {
        const levelOrder = { beginner: 1, intermediate: 2, advanced: 3, expert: 4 };
        const aLevel = levelOrder[a.level as keyof typeof levelOrder] || 0;
        const bLevel = levelOrder[b.level as keyof typeof levelOrder] || 0;
        
        return sortOption.direction === 'asc' ? aLevel - bLevel : bLevel - aLevel;
      }

      // Handle category sorting
      if (sortOption.key === 'category') {
        const categoryOrder = { 'frontend': 1, 'backend': 2, 'ai-ml': 3, 'tools': 4, 'soft-skills': 5 };
        const aCategory = categoryOrder[a.category as keyof typeof categoryOrder] || 0;
        const bCategory = categoryOrder[b.category as keyof typeof categoryOrder] || 0;
        
        return sortOption.direction === 'asc' ? aCategory - bCategory : bCategory - aCategory;
      }

      // Default string/number sorting
      if (sortOption.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  },

  /**
   * Get skill level percentage for progress bars
   */
  getSkillLevelPercentage: (level: Skill['level']): number => {
    const levelMap = {
      beginner: 25,
      intermediate: 50,
      advanced: 75,
      expert: 100,
    };
    return levelMap[level] || 0;
  },

  /**
   * Get skill level color
   */
  getSkillLevelColor: (level: Skill['level']): string => {
    const colorMap = {
      beginner: '#EF4444', // red
      intermediate: '#F59E0B', // amber
      advanced: '#3B82F6', // blue
      expert: '#10B981', // emerald
    };
    return colorMap[level] || '#6B7280';
  },

  /**
   * Get category icon
   */
  getCategoryIcon: (category: Skill['category']): string => {
    const iconMap = {
      frontend: '🎨',
      backend: '⚙️',
      'ai-ml': '🤖',
      tools: '🛠️',
      'soft-skills': '🧠',
    };
    return iconMap[category] || '📋';
  },

  /**
   * Get category display name
   */
  getCategoryDisplayName: (category: Skill['category']): string => {
    const nameMap = {
      frontend: 'Frontend Development',
      backend: 'Backend Development',
      'ai-ml': 'AI & Machine Learning',
      tools: 'Tools & Technologies',
      'soft-skills': 'Soft Skills',
    };
    return nameMap[category] || category;
  },

  /**
   * Calculate total experience years
   */
  calculateTotalExperience: (skills: Skill[]): number => {
    const experienceYears = skills.map(skill => {
      const match = skill.experience.match(/(\d+)/);
      return match ? parseInt(match[1]) : 0;
    });
    
    return Math.max(...experienceYears, 0);
  },

  /**
   * Get skills by level
   */
  getSkillsByLevel: (skills: Skill[], level: Skill['level']): Skill[] => {
    return skills.filter(skill => skill.level === level);
  },

  /**
   * Get top skills (expert level)
   */
  getTopSkills: (skills: Skill[], limit: number = 5): Skill[] => {
    return skills
      .filter(skill => skill.level === 'expert' || skill.level === 'advanced')
      .slice(0, limit);
  },

  /**
   * Format personal info for display
   */
  formatPersonalInfo: (info: PersonalInfo) => {
    return {
      ...info,
      displayTitle: info.title,
      displayLocation: info.location,
      socialLinksArray: Object.entries(info.socialLinks)
        .filter(([_, url]) => url)
        .map(([platform, url]) => ({ platform, url })),
    };
  },

  /**
   * Validate skill data
   */
  validateSkill: (skill: Partial<Skill>): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!skill.name?.trim()) {
      errors.push('Skill name is required');
    }

    if (!skill.category) {
      errors.push('Skill category is required');
    }

    if (!skill.level) {
      errors.push('Skill level is required');
    }

    if (!skill.experience?.trim()) {
      errors.push('Experience is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Generate skill summary
   */
  generateSkillSummary: (skills: Skill[]): string => {
    const categories = aboutUtils.groupSkillsByCategory(skills);
    const categoryNames = Object.keys(categories);
    const expertSkills = skills.filter(s => s.level === 'expert').length;
    
    return `${skills.length} skills across ${categoryNames.length} categories, with ${expertSkills} expert-level proficiencies`;
  },

  /**
   * Search skills
   */
  searchSkills: (skills: Skill[], query: string): Skill[] => {
    if (!query.trim()) return skills;

    const lowercaseQuery = query.toLowerCase();
    return skills.filter(skill =>
      skill.name.toLowerCase().includes(lowercaseQuery) ||
      skill.description?.toLowerCase().includes(lowercaseQuery) ||
      skill.category.toLowerCase().includes(lowercaseQuery) ||
      skill.level.toLowerCase().includes(lowercaseQuery)
    );
  },

  /**
   * Export skills data
   */
  exportSkillsData: (skills: Skill[], format: 'json' | 'csv' = 'json'): string => {
    if (format === 'csv') {
      const headers = ['Name', 'Category', 'Level', 'Experience', 'Description'];
      const rows = skills.map(skill => [
        skill.name,
        skill.category,
        skill.level,
        skill.experience,
        skill.description || '',
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    return JSON.stringify(skills, null, 2);
  },
};
