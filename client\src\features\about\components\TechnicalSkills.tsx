/**
 * Technical skills section component
 */

import React from 'react';

interface TechnicalSkillsProps {
  className?: string;
}

export default function TechnicalSkills({ className = '' }: TechnicalSkillsProps) {
  return (
    <div className={`space-y-8 ${className}`}>
      <div>
        <h3
          data-animate
          className="text-2xl md:text-3xl font-bold text-[#4A90E2] mb-6 tracking-wide"
        >
          Technical Arsenal
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Frontend Technologies */}
          <div>
            <h5
              data-animate
              className="text-lg font-semibold text-[#F5F5F5] mb-3"
            >
              Frontend & UX
            </h5>
            <ul className="space-y-2">
              <li
                data-animate
                className="text-gray-400 text-sm"
              >
                React • TypeScript • Next.js
              </li>
              <li
                data-animate
                className="text-gray-400 text-sm"
              >
                GSAP • Three.js • Tailwind
              </li>
              <li
                data-animate
                className="text-gray-400 text-sm"
              >
                WebGL • Framer Motion 
              </li>
            </ul>
          </div>

          {/* Backend & AI */}
          <div>
            <h5
              data-animate
              className="text-lg font-semibold text-[#F5F5F5] mb-3"
            >
              Backend & AI
            </h5>
            <ul className="space-y-2">
              <li
                data-animate
                className="text-gray-400 text-sm"
              >
                Python • Node.js • FastAPI
              </li>
              <li
                data-animate
                className="text-gray-400 text-sm"
              >
                Azure AI • OpenAI • LangChain
              </li>
              <li
                data-animate
                className="text-gray-400 text-sm"
              >
                PostgreSQL • MongoDB • Redis
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Azure Certifications */}
      <div>
        <h4
          data-animate
          className="text-xl font-semibold text-[#FF3366] mb-4"
        >
          Azure Certifications
        </h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {[
            'AI Engineer Associate',
            'Data Scientist Associate', 
            'Solutions Architect Expert',
            'Developer Associate',
            'Data Engineer Associate',
            'AI Fundamentals',
            'Data Fundamentals',
            'Azure Fundamentals',
            'Security Fundamentals'
          ].map((cert, index) => (
            <div
              key={cert}
              data-animate
              className="bg-[#1A1A1A] border border-gray-700 rounded-lg p-3 text-center hover:border-[#4A90E2] transition-colors duration-300"
            >
              <span className="text-gray-300 text-sm font-medium">{cert}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Development Philosophy */}
      <div>
        <h4
          data-animate
          className="text-xl font-semibold text-[#4A90E2] mb-4"
        >
          Development Philosophy
        </h4>
        <div className="space-y-3">
          <div
            data-animate
            className="flex items-start"
          >
            <span className="text-[#FF3366] mr-3 text-lg">▲</span>
            <span className="text-gray-300">
              <strong className="text-white">Performance First:</strong> Every animation, every interaction optimized for 60fps
            </span>
          </div>
          <div
            data-animate
            className="flex items-start"
          >
            <span className="text-[#4A90E2] mr-3 text-lg">●</span>
            <span className="text-gray-300">
              <strong className="text-white">Accessibility Matters:</strong> Beautiful experiences that work for everyone
            </span>
          </div>
          <div
            data-animate
            className="flex items-start"
          >
            <span className="text-[#FF3366] mr-3 text-lg">■</span>
            <span className="text-gray-300">
              <strong className="text-white">AI-Augmented Development:</strong> Leveraging AI tools to push creative boundaries
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
