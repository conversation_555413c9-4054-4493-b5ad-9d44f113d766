/**
 * About Animations Hook
 * Specialized GSAP animations for about section components
 */

import { useRef, useCallback } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { getAccessibilityConfig } from '@/utils';
import { AboutAnimationConfig, ScannerAnimationProps, SkillAnimationConfig } from '../types';

export const useAboutAnimations = () => {
  const { gsap, isAvailable } = useGSAP();
  const timelineRef = useRef<any>(null);
  const { reduceMotion } = getAccessibilityConfig();

  const defaultConfig: AboutAnimationConfig = {
    duration: 0.8,
    ease: 'power2.out',
    stagger: 0.1,
    reduceMotion,
  };

  const animateAboutSection = useCallback((
    sectionRef: React.RefObject<HTMLElement>,
    config: Partial<AboutAnimationConfig> = {}
  ) => {
    if (!isAvailable || !sectionRef.current || reduceMotion) return;

    const finalConfig = { ...defaultConfig, ...config };
    const elements = sectionRef.current.querySelectorAll('[data-animate-about]');

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline();

    timelineRef.current
      .set(elements, {
        opacity: 0,
        y: 50,
        scale: 0.9,
      })
      .to(elements, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: finalConfig.duration,
        ease: finalConfig.ease,
        stagger: finalConfig.stagger,
      });

    return timelineRef.current;
  }, [gsap, isAvailable, reduceMotion]);

  const animateSkillBars = useCallback((
    skillsRef: React.RefObject<HTMLElement>,
    config: SkillAnimationConfig = {
      type: 'progress-bar',
      duration: 1.5,
      delay: 0.2,
      easing: 'power2.out',
    }
  ) => {
    if (!isAvailable || !skillsRef.current || reduceMotion) {
      // Fallback: show skills immediately
      const progressBars = skillsRef.current?.querySelectorAll('[data-skill-progress]');
      progressBars?.forEach((bar: any) => {
        bar.style.width = bar.dataset.skillLevel || '0%';
      });
      return;
    }

    const skillItems = skillsRef.current.querySelectorAll('[data-skill-item]');
    const progressBars = skillsRef.current.querySelectorAll('[data-skill-progress]');

    const tl = gsap.timeline({ delay: config.delay });

    // Animate skill items appearing
    tl.fromTo(skillItems, 
      {
        opacity: 0,
        x: -50,
      },
      {
        opacity: 1,
        x: 0,
        duration: 0.6,
        ease: config.easing,
        stagger: 0.1,
      }
    );

    // Animate progress bars filling
    progressBars.forEach((bar: any, index: number) => {
      const targetWidth = bar.dataset.skillLevel || '0%';
      
      tl.fromTo(bar,
        { width: '0%' },
        {
          width: targetWidth,
          duration: config.duration,
          ease: config.easing,
        },
        `-=${config.duration - (index * 0.1)}`
      );
    });

    return tl;
  }, [gsap, isAvailable, reduceMotion]);

  const animateScannerEffect = useCallback((
    scannerRef: React.RefObject<HTMLElement>,
    props: ScannerAnimationProps
  ) => {
    if (!isAvailable || !scannerRef.current || reduceMotion) {
      if (props.onScanComplete) props.onScanComplete();
      return;
    }

    const { scanLines = 3, speed = 2, color = '#00ff00', onScanComplete } = props;
    
    // Create scan lines
    const scanLineElements: HTMLElement[] = [];
    for (let i = 0; i < scanLines; i++) {
      const scanLine = document.createElement('div');
      scanLine.className = 'absolute left-0 w-full h-0.5 pointer-events-none';
      scanLine.style.backgroundColor = color;
      scanLine.style.boxShadow = `0 0 10px ${color}`;
      scanLine.style.opacity = '0.8';
      scannerRef.current.appendChild(scanLine);
      scanLineElements.push(scanLine);
    }

    const tl = gsap.timeline({
      onComplete: () => {
        // Clean up scan lines
        scanLineElements.forEach(line => {
          if (line.parentNode) {
            line.parentNode.removeChild(line);
          }
        });
        if (onScanComplete) onScanComplete();
      },
    });

    // Animate scan lines moving down
    scanLineElements.forEach((line, index) => {
      tl.fromTo(line,
        {
          top: '0%',
          opacity: 0,
        },
        {
          top: '100%',
          opacity: 0.8,
          duration: speed,
          ease: 'none',
          repeat: 2,
        },
        index * 0.3
      );
    });

    return tl;
  }, [gsap, isAvailable, reduceMotion]);

  const animateAIBrain = useCallback((
    brainRef: React.RefObject<HTMLElement>,
    isActive: boolean = true
  ) => {
    if (!isAvailable || !brainRef.current || reduceMotion) {
      if (brainRef.current) {
        brainRef.current.style.opacity = '1';
      }
      return;
    }

    if (isActive) {
      // Pulsing glow effect
      return gsap.to(brainRef.current, {
        scale: 1.05,
        filter: 'brightness(1.2) drop-shadow(0 0 20px #4A90E2)',
        duration: 2,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
      });
    } else {
      return gsap.to(brainRef.current, {
        scale: 1,
        filter: 'brightness(1) drop-shadow(0 0 5px #4A90E2)',
        duration: 0.5,
        ease: 'power2.out',
      });
    }
  }, [gsap, isAvailable, reduceMotion]);

  const animatePersonalInfo = useCallback((
    infoRef: React.RefObject<HTMLElement>
  ) => {
    if (!isAvailable || !infoRef.current || reduceMotion) return;

    const avatar = infoRef.current.querySelector('[data-avatar]');
    const textElements = infoRef.current.querySelectorAll('[data-text-animate]');

    const tl = gsap.timeline();

    // Animate avatar
    if (avatar) {
      tl.fromTo(avatar,
        {
          scale: 0,
          rotation: -180,
          opacity: 0,
        },
        {
          scale: 1,
          rotation: 0,
          opacity: 1,
          duration: 1,
          ease: 'back.out(1.7)',
        }
      );
    }

    // Animate text elements
    if (textElements.length > 0) {
      tl.fromTo(textElements,
        {
          opacity: 0,
          y: 30,
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: 'power2.out',
          stagger: 0.1,
        },
        '-=0.5'
      );
    }

    return tl;
  }, [gsap, isAvailable, reduceMotion]);

  const createScrollTriggerAnimation = useCallback((
    element: React.RefObject<HTMLElement>,
    animationType: 'fade' | 'slide' | 'scale' | 'rotate' = 'fade'
  ) => {
    if (!isAvailable || !element.current || reduceMotion) return;

    const animations = {
      fade: { opacity: 0 },
      slide: { opacity: 0, y: 100 },
      scale: { opacity: 0, scale: 0.5 },
      rotate: { opacity: 0, rotation: 180 },
    };

    const fromVars = animations[animationType];
    const toVars = { opacity: 1, y: 0, scale: 1, rotation: 0 };

    return gsap.fromTo(element.current, fromVars, {
      ...toVars,
      duration: 1,
      ease: 'power2.out',
      scrollTrigger: {
        trigger: element.current,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse',
      },
    });
  }, [gsap, isAvailable, reduceMotion]);

  const cleanup = useCallback(() => {
    if (timelineRef.current) {
      timelineRef.current.kill();
      timelineRef.current = null;
    }
  }, []);

  return {
    animateAboutSection,
    animateSkillBars,
    animateScannerEffect,
    animateAIBrain,
    animatePersonalInfo,
    createScrollTriggerAnimation,
    cleanup,
    isAvailable,
  };
};

export default useAboutAnimations;
