/**
 * Spaceship components module index
 */

// Base components
export { default as BaseSpace<PERSON>, withSpaceshipAnimation, useSpaceshipComponent } from './BaseSpaceship';

// Animation components
export {
  SpaceshipBeam,
  SpaceshipGlow,
  SpaceshipLights,
  SpaceshipExhaust,
  AnimatedSpaceshipWrapper
} from './SpaceshipAnimations';

// Types and configurations
export type {
  BaseSpaceshipProps,
  SpaceshipSize,
  SpaceshipType,
  SpaceshipVariant,
  SpaceshipSizeConfig,
  SpaceshipAnimationState,
  SpaceshipAnimationConfig,
  AnimatedSpaceshipProps,
  SpaceshipConfig,
  SpaceshipRefs,
  SpaceshipAnimationContext,
  SpaceshipEventHandlers
} from './types';

export {
  SPACESHIP_SIZE_CONFIGS,
  DEFAULT_UFO_CONFIG,
  DEFAULT_ROCKET_CONFIG,
  getSpaceshipConfig,
  mergeSpaceshipConfig
} from './types';
