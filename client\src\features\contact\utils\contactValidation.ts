/**
 * Contact Validation Utilities
 * Validation functions and rules for contact forms
 */

import { ContactFormData, ContactFormErrors, ContactValidationRules } from '../types';

export const contactValidation = {
  // Default validation rules
  defaultRules: {
    name: {
      required: true,
      minLength: 2,
      maxLength: 50,
    },
    email: {
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
    message: {
      required: true,
      minLength: 10,
      maxLength: 1000,
    },
    phone: {
      required: false,
      pattern: /^[\+]?[1-9][\d]{0,15}$/,
    },
  } as ContactValidationRules,

  /**
   * Validate individual field
   */
  validateField: (
    field: keyof ContactFormData,
    value: string,
    rules: ContactValidationRules = contactValidation.defaultRules
  ): string | null => {
    const fieldRules = rules[field];
    if (!fieldRules) return null;

    // Required validation
    if (fieldRules.required && (!value || value.trim().length === 0)) {
      return `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
    }

    // Skip other validations if field is empty and not required
    if (!value || value.trim().length === 0) {
      return null;
    }

    // Length validations
    if ('minLength' in fieldRules && value.length < fieldRules.minLength) {
      return `${field.charAt(0).toUpperCase() + field.slice(1)} must be at least ${fieldRules.minLength} characters`;
    }

    if ('maxLength' in fieldRules && value.length > fieldRules.maxLength) {
      return `${field.charAt(0).toUpperCase() + field.slice(1)} must be no more than ${fieldRules.maxLength} characters`;
    }

    // Pattern validation
    if ('pattern' in fieldRules && !fieldRules.pattern.test(value)) {
      switch (field) {
        case 'email':
          return 'Please enter a valid email address';
        case 'phone':
          return 'Please enter a valid phone number';
        default:
          return `${field.charAt(0).toUpperCase() + field.slice(1)} format is invalid`;
      }
    }

    return null;
  },

  /**
   * Validate entire contact form
   */
  validateContactForm: (
    data: ContactFormData,
    rules: ContactValidationRules = contactValidation.defaultRules
  ): { isValid: boolean; errors: ContactFormErrors } => {
    const errors: ContactFormErrors = {};

    // Validate each field
    Object.keys(data).forEach(key => {
      const field = key as keyof ContactFormData;
      const error = contactValidation.validateField(field, data[field] || '', rules);
      if (error) {
        errors[field] = error;
      }
    });

    // Additional cross-field validations
    if (data.email && data.name) {
      // Check if email and name are suspiciously similar (potential spam)
      const emailLocal = data.email.split('@')[0].toLowerCase();
      const nameNormalized = data.name.toLowerCase().replace(/\s+/g, '');
      
      if (emailLocal === nameNormalized && nameNormalized.length < 4) {
        errors.general = 'Please provide a valid name and email combination';
      }
    }

    // Check for common spam patterns in message
    if (data.message) {
      const spamPatterns = [
        /\b(viagra|cialis|loan|casino|poker)\b/i,
        /\b(click here|visit now|act now)\b/i,
        /\$\d+/,
        /(http|https):\/\/[^\s]+/,
      ];

      const hasSpamPattern = spamPatterns.some(pattern => pattern.test(data.message));
      if (hasSpamPattern) {
        errors.message = 'Message contains prohibited content';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  },

  /**
   * Sanitize form data
   */
  sanitizeFormData: (data: ContactFormData): ContactFormData => {
    const sanitized: ContactFormData = {
      name: data.name?.trim() || '',
      email: data.email?.trim().toLowerCase() || '',
      message: data.message?.trim() || '',
      subject: data.subject?.trim() || '',
      phone: data.phone?.trim() || '',
      company: data.company?.trim() || '',
    };

    // Remove any HTML tags from text fields
    Object.keys(sanitized).forEach(key => {
      const field = key as keyof ContactFormData;
      if (sanitized[field]) {
        sanitized[field] = sanitized[field].replace(/<[^>]*>/g, '');
      }
    });

    return sanitized;
  },

  /**
   * Check if email domain is valid
   */
  isValidEmailDomain: async (email: string): Promise<boolean> => {
    try {
      const domain = email.split('@')[1];
      if (!domain) return false;

      // Basic domain format check
      const domainPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
      return domainPattern.test(domain);
    } catch {
      return false;
    }
  },

  /**
   * Rate limiting check (client-side)
   */
  checkRateLimit: (email: string): boolean => {
    const storageKey = `contact_submissions_${email}`;
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    try {
      const stored = localStorage.getItem(storageKey);
      if (!stored) {
        localStorage.setItem(storageKey, JSON.stringify({ count: 1, timestamp: now }));
        return true;
      }

      const data = JSON.parse(stored);
      
      // Reset if more than an hour has passed
      if (now - data.timestamp > oneHour) {
        localStorage.setItem(storageKey, JSON.stringify({ count: 1, timestamp: now }));
        return true;
      }

      // Check if under limit (max 3 submissions per hour)
      if (data.count >= 3) {
        return false;
      }

      // Increment count
      localStorage.setItem(storageKey, JSON.stringify({ 
        count: data.count + 1, 
        timestamp: data.timestamp 
      }));
      return true;
    } catch {
      return true; // Allow if localStorage fails
    }
  },

  /**
   * Generate validation summary
   */
  getValidationSummary: (errors: ContactFormErrors): string => {
    const errorCount = Object.keys(errors).length;
    if (errorCount === 0) return 'Form is valid';
    if (errorCount === 1) return '1 error found';
    return `${errorCount} errors found`;
  },
};
