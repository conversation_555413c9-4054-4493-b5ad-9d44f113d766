# Services Guide

## Overview

This guide covers all shared services in the portfolio website. Services provide cross-feature functionality and utilities that can be used throughout the application.

## API Service

### Overview
Centralized HTTP client for all API communications with built-in caching, error handling, and retry logic.

### Basic Usage
```typescript
import { apiService } from '@/services';

// GET request
const response = await apiService.get('/api/projects');

// POST request
const result = await apiService.post('/api/contact', formData);

// With configuration
const data = await apiService.get('/api/projects', {
  cache: true,
  timeout: 5000,
  retries: 3
});
```

### Configuration
```typescript
// Set base URL
apiService.setBaseUrl('https://api.example.com');

// Set default headers
apiService.setDefaultHeaders({
  'Authorization': 'Bearer token',
  'X-API-Key': 'your-api-key'
});
```

### Feature-Specific APIs
```typescript
import { portfolioApi, contactApi, aboutApi } from '@/services';

// Portfolio API
const projects = await portfolioApi.getProjects();
const project = await portfolioApi.getProject('project-id');
await portfolioApi.createProject(projectData);

// Contact API
await contactApi.submitForm(formData);
const contactInfo = await contactApi.getContactInfo();

// About API
const personalInfo = await aboutApi.getPersonalInfo();
const skills = await aboutApi.getSkills();
```

### Response Format
```typescript
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
  status: number;
}
```

### Error Handling
```typescript
const response = await apiService.get('/api/data');

if (!response.success) {
  console.error('API Error:', response.error);
  // Handle error
}
```

## State Manager

### Overview
Provides global state management and enhanced local state utilities with persistence and validation.

### Global State
```typescript
import { createStateManager, useGlobalState } from '@/services';

// Create state manager
interface AppState {
  user: User | null;
  theme: 'light' | 'dark';
  notifications: Notification[];
}

const appStateManager = createStateManager<AppState>({
  user: null,
  theme: 'dark',
  notifications: []
}, {
  persist: true,
  storageKey: 'app-state'
});

// Use in components
const [state, setState] = useGlobalState(appStateManager);

// Update state
setState({ theme: 'light' });

// Use with selector
const [theme] = useGlobalState(appStateManager, state => state.theme);
```

### Enhanced Local State
```typescript
import { useEnhancedState } from '@/services';

const [formData, setFormData, { reset, isValid }] = useEnhancedState(
  { name: '', email: '' },
  {
    persist: true,
    storageKey: 'form-data',
    debounceMs: 500,
    validator: (data) => data.name.length > 0 && data.email.includes('@')
  }
);

// Update state
setFormData({ name: 'John' });

// Reset to initial state
reset();

// Check validity
if (isValid) {
  // Submit form
}
```

### Pre-built State Managers
```typescript
import { uiStateManager, userPreferencesManager } from '@/services';

// UI State
const [uiState, setUIState] = useGlobalState(uiStateManager);
setUIState({ 
  theme: 'dark',
  sidebarOpen: true,
  loading: false 
});

// User Preferences
const [preferences, setPreferences] = useGlobalState(userPreferencesManager);
setPreferences({
  animationsEnabled: false,
  accessibility: { reduceMotion: true }
});
```

## Animation Service

### Overview
Centralized GSAP animation utilities with accessibility support and common animation patterns.

### Basic Usage
```typescript
import { animationService } from '@/services';

// Check reduced motion preference
const reduceMotion = animationService.checkReducedMotion();

// Get default config with accessibility
const config = animationService.getDefaultConfig({
  duration: 1,
  ease: 'power2.out'
});
```

### Animation Creators
```typescript
// Fade in animation
const fadeAnimation = animationService.createFadeIn(gsap, '.element', {
  duration: 0.8,
  delay: 0.2
});

// Slide in animation
const slideAnimation = animationService.createSlideIn(gsap, '.element', 'up', {
  duration: 1,
  stagger: 0.1
});

// Scale animation
const scaleAnimation = animationService.createScale(gsap, '.element', 0, 1, {
  ease: 'back.out(1.7)'
});

// Stagger animation
const staggerAnimation = animationService.createStagger(gsap, '.elements', 'fadeIn', {
  stagger: 0.1,
  duration: 0.8
});
```

### Animation Presets
```typescript
// Use predefined animation patterns
animationService.presets.fadeInUp(gsap, '.element');
animationService.presets.fadeInDown(gsap, '.element');
animationService.presets.scaleIn(gsap, '.element');
animationService.presets.pulse(gsap, '.element');
```

### Hover Animations
```typescript
const { enter, leave } = animationService.createHoverAnimation(
  gsap,
  '.button',
  { scale: 1.1, rotation: 5 },
  { duration: 0.3 }
);

// Apply to element
element.addEventListener('mouseenter', enter);
element.addEventListener('mouseleave', leave);
```

## Validation Service

### Overview
Comprehensive validation utilities with pre-built schemas, sanitization, and file validation.

### Field Validation
```typescript
import { validationService } from '@/services';

// Validate single field
const error = validationService.validateField(
  '<EMAIL>',
  { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
  'Email'
);

if (error) {
  console.log('Validation error:', error);
}
```

### Form Validation
```typescript
// Validate entire form
const result = validationService.validate(
  { name: 'John', email: '<EMAIL>' },
  {
    name: { required: true, minLength: 2 },
    email: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ }
  }
);

if (!result.isValid) {
  console.log('Errors:', result.errors);
  console.log('First error:', result.firstError);
}
```

### Validation Rules
```typescript
// Create validation rules
const rules = {
  name: [
    validationService.createRules.required('Name is required'),
    validationService.createRules.minLength(2, 'Name too short'),
    validationService.createRules.maxLength(50, 'Name too long')
  ],
  email: [
    validationService.createRules.required(),
    validationService.createRules.email()
  ],
  age: [
    validationService.createRules.custom(value => 
      value < 18 ? 'Must be 18 or older' : null
    )
  ]
};
```

### Pre-built Schemas
```typescript
// Use pre-built validation schemas
const contactResult = validationService.validate(
  contactFormData,
  validationService.schemas.contact
);

const userResult = validationService.validate(
  userRegistrationData,
  validationService.schemas.user
);

const projectResult = validationService.validate(
  projectData,
  validationService.schemas.project
);
```

### Data Sanitization
```typescript
// Sanitize user input
const cleanData = validationService.sanitize({
  name: '  John Doe  ',
  message: '<script>alert("xss")</script>Hello <b>world</b>',
  email: '  <EMAIL>  '
});

// Result:
// {
//   name: 'John Doe',
//   message: 'Hello world',
//   email: '<EMAIL>'
// }
```

### File Validation
```typescript
// Validate file uploads
const fileResult = validationService.validateFile(file, {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png'],
  allowedExtensions: ['jpg', 'jpeg', 'png']
});

if (!fileResult.isValid) {
  console.log('File errors:', fileResult.errors);
}
```

### Email Domain Validation
```typescript
// Validate email domain
const isValidDomain = await validationService.validateEmailDomain('<EMAIL>');
```

## Bundle Optimization Service

### Overview
Manages code splitting, component preloading, and bundle performance monitoring.

### Chunk Preloading
```typescript
import { bundleOptimizationService } from '@/services';

// Preload a chunk
await bundleOptimizationService.preloadChunk(
  'portfolio-section',
  () => import('@/features/portfolio')
);

// Check preload status
const status = bundleOptimizationService.getPreloadStatus();
console.log('Preloaded chunks:', status.preloaded);
console.log('Loading chunks:', status.loading);
```

### Preloading Strategies
```typescript
// Preload on hover
const cleanup = bundleOptimizationService.preloadOnHover(
  element,
  'contact-section',
  () => import('@/features/contact')
);

// Preload on viewport entry
const cleanup2 = bundleOptimizationService.preloadOnViewport(
  element,
  'about-section',
  () => import('@/features/about'),
  { rootMargin: '100px' }
);

// Preload on idle
bundleOptimizationService.preloadOnIdle(
  'video-player',
  () => import('@/features/portfolio/components/VideoPlayer')
);
```

### Performance Monitoring
```typescript
// Measure bundle performance
bundleOptimizationService.measureBundlePerformance();

// Get metrics
const metrics = bundleOptimizationService.getBundleMetrics();
console.log('Load time:', metrics.loadTime);
console.log('Total chunks:', metrics.totalChunks);

// Get recommendations
const recommendations = bundleOptimizationService.getBundleRecommendations();
console.log('Optimization suggestions:', recommendations);
```

### Development Analysis
```typescript
// Enable bundle analysis in development
if (process.env.NODE_ENV === 'development') {
  bundleOptimizationService.enableBundleAnalysis();
}
```

## Service Integration Patterns

### Service Composition
```typescript
// Combine multiple services
const useDataWithValidation = () => {
  const [data, setData] = useState({});
  const [errors, setErrors] = useState({});

  const updateData = useCallback(async (newData) => {
    // Validate data
    const validation = validationService.validate(newData, schema);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    // Sanitize data
    const cleanData = validationService.sanitize(newData);

    // Save via API
    const response = await apiService.post('/api/data', cleanData);
    if (response.success) {
      setData(response.data);
      setErrors({});
    }
  }, []);

  return { data, errors, updateData };
};
```

### Service Middleware
```typescript
// Add request/response interceptors
apiService.setDefaultHeaders({
  'X-Request-ID': () => crypto.randomUUID()
});

// Add validation middleware
const validateAndSubmit = async (data, schema) => {
  const validation = validationService.validate(data, schema);
  if (!validation.isValid) {
    throw new Error(validation.firstError);
  }

  const cleanData = validationService.sanitize(data);
  return apiService.post('/api/submit', cleanData);
};
```

### Service Caching
```typescript
// Implement service-level caching
const cachedApiCall = async (endpoint, options = {}) => {
  const cacheKey = `${endpoint}-${JSON.stringify(options)}`;
  
  // Check cache first
  const cached = localStorage.getItem(cacheKey);
  if (cached && !options.skipCache) {
    return JSON.parse(cached);
  }

  // Make API call
  const response = await apiService.get(endpoint, options);
  
  // Cache successful responses
  if (response.success) {
    localStorage.setItem(cacheKey, JSON.stringify(response));
  }

  return response;
};
```

## Error Handling

### Service Error Patterns
```typescript
// Centralized error handling
const handleServiceError = (error, context) => {
  console.error(`Service error in ${context}:`, error);
  
  // Log to monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Send to error tracking service
  }

  // Show user-friendly message
  return {
    success: false,
    error: 'Something went wrong. Please try again.',
    details: error.message
  };
};

// Use in service calls
try {
  const result = await apiService.get('/api/data');
  return result;
} catch (error) {
  return handleServiceError(error, 'data-fetch');
}
```

### Retry Logic
```typescript
// Implement retry with exponential backoff
const retryWithBackoff = async (fn, maxRetries = 3) => {
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      const delay = Math.pow(2, attempt) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};
```

## Testing Services

### Service Testing Patterns
```typescript
// Mock services for testing
vi.mock('@/services/apiService', () => ({
  apiService: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  }
}));

// Test service integration
it('should handle API errors gracefully', async () => {
  const mockError = new Error('Network error');
  vi.mocked(apiService.get).mockRejectedValue(mockError);

  const result = await dataService.fetchData();
  
  expect(result.success).toBe(false);
  expect(result.error).toBeDefined();
});
```

### Service Mocking
```typescript
// Create service mocks
const createMockApiService = () => ({
  get: vi.fn().mockResolvedValue({ success: true, data: {} }),
  post: vi.fn().mockResolvedValue({ success: true, data: {} }),
  put: vi.fn().mockResolvedValue({ success: true, data: {} }),
  delete: vi.fn().mockResolvedValue({ success: true, data: {} }),
});

// Use in tests
const mockApi = createMockApiService();
```

## Best Practices

### Service Design
1. **Single Responsibility**: Each service should have one clear purpose
2. **Interface Consistency**: Use consistent interfaces across services
3. **Error Handling**: Implement comprehensive error handling
4. **Type Safety**: Full TypeScript coverage

### Performance
1. **Caching**: Implement appropriate caching strategies
2. **Lazy Loading**: Load services only when needed
3. **Debouncing**: Debounce API calls and expensive operations
4. **Monitoring**: Track service performance

### Testing
1. **Unit Tests**: Test each service method in isolation
2. **Integration Tests**: Test service interactions
3. **Mock Dependencies**: Mock external dependencies
4. **Error Scenarios**: Test error handling paths

### Security
1. **Input Validation**: Validate all inputs
2. **Sanitization**: Sanitize user data
3. **Authentication**: Handle auth tokens securely
4. **Rate Limiting**: Implement client-side rate limiting
