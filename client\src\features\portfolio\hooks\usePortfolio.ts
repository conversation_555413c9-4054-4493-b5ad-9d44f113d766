/**
 * Portfolio Feature Hook
 * Centralized state management and logic for portfolio functionality
 */

import { useState, useCallback, useMemo } from 'react';
import { Project, PortfolioHookReturn, ProjectFilter, SortOption } from '../types';
import { portfolioUtils } from '../utils/portfolioUtils';
import { PROJECTS } from '@/constants';

export const usePortfolio = (): PortfolioHookReturn => {
  const [projects, setProjects] = useState<Project[]>(
    PROJECTS.map(portfolioUtils.transformProjectData)
  );
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    type: [] as string[],
    technology: [] as string[],
    status: [] as string[],
  });

  const selectProject = useCallback((project: Project | null) => {
    setSelectedProject(project);
  }, []);

  const openVideoModal = useCallback((project: Project) => {
    setSelectedProject(project);
    setIsVideoModalOpen(true);
  }, []);

  const closeVideoModal = useCallback(() => {
    setIsVideoModalOpen(false);
    setSelectedProject(null);
  }, []);

  const updateFilters = useCallback((newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({
      type: [],
      technology: [],
      status: [],
    });
  }, []);

  // Filtered and sorted projects
  const filteredProjects = useMemo(() => {
    return portfolioUtils.filterProjects(projects, filters);
  }, [projects, filters]);

  return {
    projects: filteredProjects,
    selectedProject,
    isVideoModalOpen,
    loading,
    error,
    filters,
    setProjects,
    selectProject,
    openVideoModal,
    closeVideoModal,
    setLoading,
    setError,
    updateFilters,
    clearFilters,
  };
};

export default usePortfolio;
