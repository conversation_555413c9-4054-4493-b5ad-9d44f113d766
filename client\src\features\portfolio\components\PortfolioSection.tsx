/**
 * Refactored Portfolio section component using smaller, focused components
 */

import React, { useRef, useCallback, useState } from 'react';
import { useScrollTrigger } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { Z_INDEX } from '@/constants';
import { AnimatedContent } from '@/features/about';
import PortfolioGrid from './PortfolioGrid';
import VideoModal from './VideoModal';

interface PortfolioSectionProps {
  className?: string;
  id?: string;
  showFilters?: boolean;
}

export default function PortfolioSection({
  className = '',
  id = 'portfolio',
  showFilters = false
}: PortfolioSectionProps) {
  const sectionRef = useRef<HTMLElement>(null);
  const [animationPhase, setAnimationPhase] = useState<'idle' | 'content' | 'complete'>('idle');
  const [videoModal, setVideoModal] = useState<{
    isOpen: boolean;
    videoSrc: string;
    title: string;
  }>({
    isOpen: false,
    videoSrc: '',
    title: ''
  });
  
  const { createScrollTrigger } = useScrollTrigger();
  const { handleError } = useErrorHandler();

  const handleVideoClick = useCallback((videoSrc: string, title: string) => {
    try {
      setVideoModal({
        isOpen: true,
        videoSrc,
        title
      });
    } catch (error) {
      handleError(error as Error);
    }
  }, [handleError]);

  const handleCloseModal = useCallback(() => {
    try {
      setVideoModal(prev => ({
        ...prev,
        isOpen: false
      }));
    } catch (error) {
      handleError(error as Error);
    }
  }, [handleError]);

  const handleContentComplete = useCallback(() => {
    setAnimationPhase('complete');
  }, []);

  const initializeAnimations = useCallback(() => {
    try {
      setAnimationPhase('content');
    } catch (error) {
      handleError(error as Error);
    }
  }, [handleError]);

  // Set up scroll trigger
  React.useEffect(() => {
    if (!sectionRef.current) return;

    const trigger = createScrollTrigger({
      trigger: sectionRef.current,
      start: "top 80%",
      once: true,
      onEnter: initializeAnimations,
    });

    return () => {
      if (trigger) trigger.kill();
    };
  }, [createScrollTrigger, initializeAnimations]);

  return (
    <>
      <section
        ref={sectionRef}
        className={`min-h-screen bg-[#0F0F0F] py-20 px-4 relative overflow-hidden ${className}`}
        id={id}
        role="region"
        aria-labelledby="portfolio-heading"
      >
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1A1A1A] via-[#0F0F0F] to-[#1A1A1A] opacity-80" />
        
        <div className="max-w-6xl mx-auto relative" style={{ zIndex: Z_INDEX.CONTENT }}>
          {/* Section Title */}
          <AnimatedContent
            autoStart={animationPhase === 'content'}
            startDelay={0}
            staggerDelay={0.12}
            animationType="fadeUp"
            className="text-center mb-16"
          >
            <h2
              data-animate
              id="portfolio-heading"
              className="text-4xl md:text-5xl lg:text-6xl font-black text-[#F5F5F5] mb-6 tracking-wide"
            >
              MY WORK
            </h2>
            <p
              data-animate
              className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
            >
              A showcase of projects that blend cutting-edge AI technology with immersive user experiences.
              Each project represents a step forward in pushing the boundaries of what's possible.
            </p>
          </AnimatedContent>

          {/* Portfolio Grid */}
          <AnimatedContent
            autoStart={animationPhase === 'content'}
            startDelay={500}
            staggerDelay={0.2}
            animationType="fadeUp"
            onComplete={handleContentComplete}
          >
            <div data-animate>
              <PortfolioGrid
                onVideoClick={handleVideoClick}
                autoAnimate={animationPhase === 'content'}
                animationDelay={800}
              />
            </div>
          </AnimatedContent>

          {/* Coming Soon Notice */}
          <AnimatedContent
            autoStart={animationPhase === 'content'}
            startDelay={1200}
            animationType="fadeUp"
            className="text-center mt-16"
          >
            <div
              data-animate
              className="bg-[#1A1A1A] border border-gray-700 rounded-lg p-8 max-w-2xl mx-auto"
            >
              <h3 className="text-xl font-semibold text-[#FF3366] mb-4">
                🚀 More Projects Coming Soon
              </h3>
              <p className="text-gray-300 leading-relaxed">
                I'm constantly working on new projects that push the boundaries of AI and web technology.
                Follow my journey as I continue to build innovative solutions that make a difference.
              </p>
            </div>
          </AnimatedContent>
        </div>
      </section>

      {/* Video Modal */}
      <VideoModal
        isOpen={videoModal.isOpen}
        onClose={handleCloseModal}
        videoSrc={videoModal.videoSrc}
        title={videoModal.title}
      />
    </>
  );
}

// Export sub-components for potential reuse
export {
  PortfolioGrid,
  VideoModal
} from '../index';
export { default as VideoPlayer } from './VideoPlayer';
