# Custom Hooks Guide

## Overview

This guide covers all custom hooks in the portfolio website, including their usage, parameters, and return values. Hooks are organized by feature and shared utilities.

## Shared Hooks

### useGSAP
GSAP integration hook for animations.

```typescript
import { useGSAP } from '@/hooks/useGSAP';

const { gsap, animate, isAvailable } = useGSAP();

// Usage
if (isAvailable) {
  gsap.to(element, { opacity: 1, duration: 0.5 });
}
```

**Returns:**
- `gsap`: GSAP instance
- `animate`: Animation helper function
- `isAvailable`: Boolean indicating GSAP availability

### useLazyLoading
Manages component lazy loading and preloading strategies.

```typescript
import { useLazyLoading } from '@/hooks/useLazyLoading';

const { elementRef, preload, isPreloaded, isLoading } = useLazyLoading(
  'portfolio-section',
  () => import('@/features/portfolio'),
  {
    preloadOnHover: true,
    preloadOnViewport: true,
    preloadOnIdle: false,
  }
);
```

**Parameters:**
- `chunkName`: Unique identifier for the chunk
- `importFn`: Function that returns the import promise
- `options`: Preloading configuration

**Returns:**
- `elementRef`: Ref to attach to DOM element
- `preload`: Manual preload function
- `isPreloaded`: Whether chunk is already loaded
- `isLoading`: Whether chunk is currently loading

### useNavigationPreloading
Preloads components based on navigation intent.

```typescript
import { useNavigationPreloading } from '@/hooks/useLazyLoading';

const { preloadRoute, preloadOnNavigation, preloadedRoutes } = useNavigationPreloading();

// Preload when user hovers over navigation
const handleNavHover = () => {
  preloadOnNavigation('portfolio');
};
```

**Returns:**
- `preloadRoute`: Preload specific route
- `preloadOnNavigation`: Preload on navigation intent
- `preloadedRoutes`: Array of preloaded routes

### useBundlePerformance
Monitors and analyzes bundle performance.

```typescript
import { useBundlePerformance } from '@/hooks/useLazyLoading';

const { metrics, recommendations, preloadStatus } = useBundlePerformance();

console.log('Bundle metrics:', metrics);
console.log('Optimization recommendations:', recommendations);
```

**Returns:**
- `metrics`: Performance metrics object
- `recommendations`: Array of optimization suggestions
- `preloadStatus`: Current preload status

### useAdaptiveLoading
Adapts loading strategy based on device capabilities.

```typescript
import { useAdaptiveLoading } from '@/hooks/useLazyLoading';

const { connectionSpeed, deviceMemory, shouldPreload, optimalStrategy } = useAdaptiveLoading();

if (shouldPreload) {
  // Implement preloading
}
```

**Returns:**
- `connectionSpeed`: Network connection speed
- `deviceMemory`: Device memory in GB
- `shouldPreload`: Whether preloading is recommended
- `optimalStrategy`: Recommended loading strategy

## Portfolio Feature Hooks

### usePortfolio
Main state management hook for portfolio functionality.

```typescript
import { usePortfolio } from '@/features/portfolio';

const {
  projects,
  selectedProject,
  isVideoModalOpen,
  loading,
  error,
  filters,
  setProjects,
  selectProject,
  openVideoModal,
  closeVideoModal,
  setLoading,
  setError,
  updateFilters,
  clearFilters
} = usePortfolio();
```

**State:**
- `projects`: Array of filtered projects
- `selectedProject`: Currently selected project
- `isVideoModalOpen`: Video modal visibility
- `loading`: Loading state
- `error`: Error message
- `filters`: Current filter settings

**Actions:**
- `setProjects`: Update projects array
- `selectProject`: Select/deselect project
- `openVideoModal`: Open video modal with project
- `closeVideoModal`: Close video modal
- `setLoading`: Set loading state
- `setError`: Set error state
- `updateFilters`: Update filter settings
- `clearFilters`: Reset all filters

### usePortfolioAnimations
GSAP animations specific to portfolio components.

```typescript
import { usePortfolioAnimations } from '@/features/portfolio';

const {
  animatePortfolioGrid,
  animateProjectCard,
  animateVideoModal,
  createScrollTrigger,
  cleanup,
  isAvailable
} = usePortfolioAnimations();

// Animate grid on mount
useEffect(() => {
  animatePortfolioGrid(gridRef);
  return cleanup;
}, []);
```

**Methods:**
- `animatePortfolioGrid`: Animate project grid entrance
- `animateProjectCard`: Animate individual card hover/focus
- `animateVideoModal`: Animate modal open/close
- `createScrollTrigger`: Create scroll-based animations
- `cleanup`: Clean up all animations
- `isAvailable`: GSAP availability status

## Contact Feature Hooks

### useContactForm
Form state management and validation for contact forms.

```typescript
import { useContactForm } from '@/features/contact';

const {
  data,
  errors,
  isSubmitting,
  isSubmitted,
  submitError,
  submitSuccess,
  updateField,
  setError,
  clearError,
  clearAllErrors,
  setSubmitting,
  setSubmitted,
  setSubmitError,
  setSubmitSuccess,
  resetForm,
  submitForm
} = useContactForm(customSubmitHandler);
```

**State:**
- `data`: Form data object
- `errors`: Validation errors
- `isSubmitting`: Submission in progress
- `isSubmitted`: Form has been submitted
- `submitError`: Submission error message
- `submitSuccess`: Submission success status

**Actions:**
- `updateField`: Update single form field
- `setError`: Set field error
- `clearError`: Clear field error
- `clearAllErrors`: Clear all errors
- `setSubmitting`: Set submission state
- `setSubmitted`: Set submitted state
- `setSubmitError`: Set submission error
- `setSubmitSuccess`: Set success state
- `resetForm`: Reset entire form
- `submitForm`: Submit form with validation

**Parameters:**
- `customSubmitHandler`: Optional custom submission function

### useContactAnimations
GSAP animations for contact components.

```typescript
import { useContactAnimations } from '@/features/contact';

const {
  animateContactSection,
  animateSpaceshipFlyIn,
  animateTextReveal,
  animateFormSubmission,
  animateParticleExplosion,
  cleanup,
  isAvailable
} = useContactAnimations();
```

**Methods:**
- `animateContactSection`: Animate section entrance
- `animateSpaceshipFlyIn`: Animate spaceship entrance
- `animateTextReveal`: Animate text reveals (normal/alien)
- `animateFormSubmission`: Animate form submission feedback
- `animateParticleExplosion`: Create particle explosion effect
- `cleanup`: Clean up animations
- `isAvailable`: GSAP availability

## About Feature Hooks

### useAbout
State management for about section data.

```typescript
import { useAbout } from '@/features/about';

const {
  personalInfo,
  skills,
  isLoading,
  error,
  activeSection,
  animationsEnabled,
  setPersonalInfo,
  setSkills,
  setLoading,
  setError,
  setActiveSection,
  toggleAnimations,
  addSkill,
  updateSkill,
  removeSkill
} = useAbout();
```

**State:**
- `personalInfo`: Personal information object
- `skills`: Array of skills
- `isLoading`: Loading state
- `error`: Error message
- `activeSection`: Currently active section
- `animationsEnabled`: Animation preference

**Actions:**
- `setPersonalInfo`: Update personal info
- `setSkills`: Update skills array
- `setLoading`: Set loading state
- `setError`: Set error state
- `setActiveSection`: Set active section
- `toggleAnimations`: Toggle animation preference
- `addSkill`: Add new skill
- `updateSkill`: Update existing skill
- `removeSkill`: Remove skill

### useAboutAnimations
GSAP animations for about section components.

```typescript
import { useAboutAnimations } from '@/features/about';

const {
  animateAboutSection,
  animateSkillBars,
  animateScannerEffect,
  animateAIBrain,
  animatePersonalInfo,
  createScrollTriggerAnimation,
  cleanup,
  isAvailable
} = useAboutAnimations();
```

**Methods:**
- `animateAboutSection`: Animate section entrance
- `animateSkillBars`: Animate skill progress bars
- `animateScannerEffect`: Create scanner animation
- `animateAIBrain`: Animate AI brain visualization
- `animatePersonalInfo`: Animate personal info section
- `createScrollTriggerAnimation`: Create scroll-based animations
- `cleanup`: Clean up animations
- `isAvailable`: GSAP availability

## Service Hooks

### useBundleOptimization
Bundle optimization utilities from the service layer.

```typescript
import { useBundleOptimization } from '@/services/bundleOptimizationService';

const {
  preloadChunk,
  preloadOnHover,
  preloadOnViewport,
  preloadOnIdle,
  getPreloadStatus,
  getBundleMetrics
} = useBundleOptimization();
```

**Methods:**
- `preloadChunk`: Manually preload a chunk
- `preloadOnHover`: Set up hover preloading
- `preloadOnViewport`: Set up viewport preloading
- `preloadOnIdle`: Set up idle preloading
- `getPreloadStatus`: Get current preload status
- `getBundleMetrics`: Get bundle performance metrics

## Hook Patterns

### State Management Pattern
```typescript
const useFeatureState = <T>(initialState: T) => {
  const [state, setState] = useState<T>(initialState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateState = useCallback((updates: Partial<T>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const resetState = useCallback(() => {
    setState(initialState);
    setError(null);
  }, [initialState]);

  return {
    state,
    loading,
    error,
    updateState,
    resetState,
    setLoading,
    setError,
  };
};
```

### Animation Hook Pattern
```typescript
const useFeatureAnimations = () => {
  const { gsap, isAvailable } = useGSAP();
  const { reduceMotion } = getAccessibilityConfig();
  const timelineRef = useRef<any>(null);

  const animateElement = useCallback((
    element: React.RefObject<HTMLElement>,
    config: AnimationConfig = {}
  ) => {
    if (!isAvailable || !element.current || reduceMotion) return;

    return gsap.to(element.current, {
      duration: 0.8,
      ease: 'power2.out',
      ...config,
    });
  }, [gsap, isAvailable, reduceMotion]);

  const cleanup = useCallback(() => {
    if (timelineRef.current) {
      timelineRef.current.kill();
      timelineRef.current = null;
    }
  }, []);

  return { animateElement, cleanup, isAvailable };
};
```

### Form Hook Pattern
```typescript
const useFeatureForm = <T extends Record<string, any>>(
  initialData: T,
  validationSchema?: ValidationSchema
) => {
  const [data, setData] = useState<T>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateField = useCallback((field: keyof T, value: any) => {
    setData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as string]) {
      setErrors(prev => ({ ...prev, [field as string]: undefined }));
    }
  }, [errors]);

  const validateForm = useCallback((): boolean => {
    if (!validationSchema) return true;
    
    const result = validationService.validate(data, validationSchema);
    setErrors(result.errors);
    return result.isValid;
  }, [data, validationSchema]);

  const submitForm = useCallback(async (
    submitHandler: (data: T) => Promise<void>
  ) => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    
    try {
      if (!validateForm()) return;
      await submitHandler(data);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [data, isSubmitting, validateForm]);

  return {
    data,
    errors,
    isSubmitting,
    updateField,
    validateForm,
    submitForm,
  };
};
```

## Best Practices

### Hook Design
1. **Single Responsibility**: Each hook should have one clear purpose
2. **Stable References**: Use useCallback and useMemo for stable references
3. **Cleanup**: Always clean up side effects in useEffect
4. **Error Handling**: Include proper error handling in async operations

### Performance
1. **Dependency Arrays**: Be careful with useEffect dependencies
2. **Memoization**: Memoize expensive calculations
3. **Debouncing**: Debounce user inputs and API calls
4. **Lazy Initialization**: Use lazy initial state when expensive

### Testing
1. **Isolated Testing**: Test hooks in isolation using renderHook
2. **Mock Dependencies**: Mock external dependencies
3. **Act Wrapper**: Use act() for state updates
4. **Cleanup Testing**: Test cleanup functions

### TypeScript
1. **Generic Hooks**: Use generics for reusable hooks
2. **Return Types**: Define explicit return types
3. **Parameter Types**: Type all parameters
4. **Inference**: Let TypeScript infer when possible
