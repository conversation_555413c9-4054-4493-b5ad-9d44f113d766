# Feature Modules Guide

## Overview

This guide explains how to work with the feature-based module system in the portfolio website. Each feature is self-contained with its own components, hooks, utilities, and types.

## Feature Module Structure

### Standard Structure
```
features/[feature-name]/
├── components/          # Feature-specific components
│   ├── [FeatureName]Section.tsx
│   ├── [SubComponent].tsx
│   └── index.ts
├── hooks/              # Custom hooks
│   ├── use[FeatureName].ts
│   ├── use[FeatureName]Animations.ts
│   └── index.ts
├── utils/              # Utility functions
│   ├── [featureName]Utils.ts
│   ├── [featureName]Validation.ts
│   └── index.ts
├── types.ts            # TypeScript definitions
└── index.ts           # Main feature exports
```

## Portfolio Feature

### Components
- **PortfolioSection**: Main portfolio display
- **PortfolioGrid**: Project grid layout
- **ProjectCard**: Individual project display
- **VideoModal**: Project video viewer
- **VideoPlayer**: Video playback component

### Hooks
```typescript
// State management
const {
  projects,
  selectedProject,
  isVideoModalOpen,
  loading,
  error,
  filters,
  setProjects,
  selectProject,
  openVideoModal,
  closeVideoModal,
  updateFilters,
  clearFilters
} = usePortfolio();

// Animations
const {
  animatePortfolioGrid,
  animateProjectCard,
  animateVideoModal,
  createScrollTrigger,
  cleanup
} = usePortfolioAnimations();
```

### Utils
```typescript
import { portfolioUtils } from '@/features/portfolio';

// Transform data
const project = portfolioUtils.transformProjectData(rawData);

// Filter projects
const filtered = portfolioUtils.filterProjects(projects, filters);

// Sort projects
const sorted = portfolioUtils.sortProjects(projects, sortOption);

// Search projects
const results = portfolioUtils.searchProjects(projects, query);

// Validate project
const validation = portfolioUtils.validateProject(projectData);
```

### Types
```typescript
interface Project {
  id: string;
  title: string;
  description: string;
  type: string;
  technologies: string[];
  status: 'completed' | 'in-progress' | 'coming-soon';
  featured: boolean;
  // ... more fields
}

interface PortfolioHookReturn {
  projects: Project[];
  selectedProject: Project | null;
  // ... state and actions
}
```

## Contact Feature

### Components
- **ContactSection**: Main contact interface
- **SpaceshipHeader**: Animated header with spaceship
- **ContactInvitation**: Call-to-action section
- **ContactForm**: Form with validation

### Hooks
```typescript
// Form management
const {
  data,
  errors,
  isSubmitting,
  isSubmitted,
  submitError,
  submitSuccess,
  updateField,
  setError,
  clearError,
  submitForm,
  resetForm
} = useContactForm();

// Animations
const {
  animateContactSection,
  animateSpaceshipFlyIn,
  animateTextReveal,
  animateFormSubmission,
  animateParticleExplosion
} = useContactAnimations();
```

### Utils
```typescript
import { contactValidation, emailService } from '@/features/contact';

// Validate form
const result = contactValidation.validateContactForm(formData);

// Sanitize data
const clean = contactValidation.sanitizeFormData(formData);

// Send email
const response = await emailService.sendContactEmail(formData);
```

### Types
```typescript
interface ContactFormData {
  name: string;
  email: string;
  message: string;
  subject?: string;
  phone?: string;
  company?: string;
}

interface ContactFormHookReturn {
  data: ContactFormData;
  errors: ContactFormErrors;
  // ... state and actions
}
```

## About Feature

### Components
- **AboutSection**: Main about display
- **ScannerAnimation**: Futuristic scanner effect
- **AIBrainOutline**: AI brain visualization
- **PersonalInfo**: Personal information display
- **TechnicalSkills**: Skills showcase

### Hooks
```typescript
// State management
const {
  personalInfo,
  skills,
  isLoading,
  error,
  activeSection,
  animationsEnabled,
  setPersonalInfo,
  setSkills,
  addSkill,
  updateSkill,
  removeSkill,
  toggleAnimations
} = useAbout();

// Animations
const {
  animateAboutSection,
  animateSkillBars,
  animateScannerEffect,
  animateAIBrain,
  animatePersonalInfo
} = useAboutAnimations();
```

### Utils
```typescript
import { aboutUtils } from '@/features/about';

// Group skills
const grouped = aboutUtils.groupSkillsByCategory(skills);

// Filter skills
const filtered = aboutUtils.filterSkills(skills, filters);

// Get skill level percentage
const percentage = aboutUtils.getSkillLevelPercentage('expert');

// Validate skill
const validation = aboutUtils.validateSkill(skillData);
```

### Types
```typescript
interface Skill {
  id: string;
  name: string;
  category: 'frontend' | 'backend' | 'ai-ml' | 'tools' | 'soft-skills';
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  experience: string;
  description?: string;
}

interface PersonalInfo {
  name: string;
  title: string;
  location: string;
  email: string;
  bio: string;
  socialLinks: Record<string, string>;
}
```

## Creating a New Feature

### 1. Create Directory Structure
```bash
mkdir -p src/features/new-feature/{components,hooks,utils}
touch src/features/new-feature/{types.ts,index.ts}
touch src/features/new-feature/components/index.ts
touch src/features/new-feature/hooks/index.ts
touch src/features/new-feature/utils/index.ts
```

### 2. Define Types
```typescript
// src/features/new-feature/types.ts
export interface NewFeatureData {
  id: string;
  name: string;
  // ... other fields
}

export interface NewFeatureState {
  data: NewFeatureData[];
  loading: boolean;
  error: string | null;
}

export interface NewFeatureActions {
  setData: (data: NewFeatureData[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export type NewFeatureHookReturn = NewFeatureState & NewFeatureActions;
```

### 3. Create Main Hook
```typescript
// src/features/new-feature/hooks/useNewFeature.ts
import { useState, useCallback } from 'react';
import { NewFeatureHookReturn, NewFeatureData } from '../types';

export const useNewFeature = (): NewFeatureHookReturn => {
  const [data, setData] = useState<NewFeatureData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ... hook logic

  return {
    data,
    loading,
    error,
    setData,
    setLoading,
    setError,
  };
};
```

### 4. Create Utils
```typescript
// src/features/new-feature/utils/newFeatureUtils.ts
import { NewFeatureData } from '../types';

export const newFeatureUtils = {
  transformData: (rawData: any): NewFeatureData => {
    // Transform logic
  },

  validateData: (data: Partial<NewFeatureData>) => {
    // Validation logic
  },

  // ... other utilities
};
```

### 5. Create Main Component
```typescript
// src/features/new-feature/components/NewFeatureSection.tsx
import React from 'react';
import { useNewFeature } from '../hooks';

const NewFeatureSection: React.FC = () => {
  const { data, loading, error } = useNewFeature();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <section>
      {/* Component JSX */}
    </section>
  );
};

export default NewFeatureSection;
```

### 6. Export Everything
```typescript
// src/features/new-feature/index.ts
// Components
export { default as NewFeatureSection } from './components/NewFeatureSection';

// Hooks
export * from './hooks';

// Utils
export { newFeatureUtils } from './utils/newFeatureUtils';

// Types
export * from './types';
```

## Best Practices

### Component Design
- **Single Responsibility**: Each component has one clear purpose
- **Composition**: Build complex UIs from simple components
- **Props Interface**: Well-defined TypeScript interfaces
- **Default Props**: Sensible defaults for optional props

### Hook Design
- **State Encapsulation**: Keep related state together
- **Action Grouping**: Group related actions in the same hook
- **Memoization**: Use useCallback and useMemo appropriately
- **Cleanup**: Proper cleanup in useEffect

### Utility Functions
- **Pure Functions**: No side effects when possible
- **Type Safety**: Full TypeScript coverage
- **Error Handling**: Graceful error handling
- **Documentation**: Clear JSDoc comments

### Testing
- **Unit Tests**: Test each function/component in isolation
- **Integration Tests**: Test feature interactions
- **Mock Dependencies**: Mock external dependencies
- **Coverage**: Aim for high test coverage

## Common Patterns

### Data Fetching
```typescript
const useFeatureData = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await apiService.get('/feature-data');
        setData(response.data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
};
```

### Form Handling
```typescript
const useFeatureForm = () => {
  const [formData, setFormData] = useState(initialData);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateField = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  const submitForm = useCallback(async () => {
    // Validation and submission logic
  }, [formData]);

  return { formData, errors, isSubmitting, updateField, submitForm };
};
```

### Animation Integration
```typescript
const useFeatureAnimations = () => {
  const { gsap, isAvailable } = useGSAP();
  const { reduceMotion } = getAccessibilityConfig();

  const animateElement = useCallback((element, config) => {
    if (!isAvailable || !element || reduceMotion) return;
    
    return gsap.to(element, config);
  }, [gsap, isAvailable, reduceMotion]);

  return { animateElement };
};
```

## Troubleshooting

### Common Issues
1. **Import Errors**: Check index.ts exports
2. **Type Errors**: Verify interface definitions
3. **Hook Dependencies**: Check useEffect dependencies
4. **Animation Issues**: Verify GSAP availability

### Debugging Tips
1. **Console Logging**: Use structured logging
2. **React DevTools**: Inspect component state
3. **Network Tab**: Check API calls
4. **Performance Tab**: Monitor performance

### Performance Optimization
1. **Memoization**: Use React.memo for expensive components
2. **Code Splitting**: Lazy load heavy components
3. **Bundle Analysis**: Monitor chunk sizes
4. **Preloading**: Implement smart preloading strategies
