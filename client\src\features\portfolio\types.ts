/**
 * Portfolio Feature Types and Interfaces
 * Centralized type definitions for the portfolio feature module
 */

export interface Project {
  id: string;
  title: string;
  description: string;
  type: string;
  technologies: string[];
  image?: string;
  video?: string;
  demoUrl?: string;
  githubUrl?: string;
  status: 'completed' | 'in-progress' | 'coming-soon';
  featured: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectCardProps {
  project: Project;
  index: number;
  onVideoPlay?: (project: Project) => void;
  className?: string;
}

export interface PortfolioGridProps {
  projects: Project[];
  loading?: boolean;
  error?: string | null;
  onProjectSelect?: (project: Project) => void;
}

export interface VideoPlayerProps {
  src: string;
  poster?: string;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  controls?: boolean;
  className?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
}

export interface VideoModalProps {
  isOpen: boolean;
  project: Project | null;
  onClose: () => void;
}

export interface PortfolioAnimationConfig {
  stagger: number;
  duration: number;
  ease: string;
  reduceMotion: boolean;
}

export interface PortfolioState {
  projects: Project[];
  selectedProject: Project | null;
  isVideoModalOpen: boolean;
  loading: boolean;
  error: string | null;
  filters: {
    type: string[];
    technology: string[];
    status: string[];
  };
}

export interface PortfolioActions {
  setProjects: (projects: Project[]) => void;
  selectProject: (project: Project | null) => void;
  openVideoModal: (project: Project) => void;
  closeVideoModal: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateFilters: (filters: Partial<PortfolioState['filters']>) => void;
  clearFilters: () => void;
}

export type PortfolioHookReturn = PortfolioState & PortfolioActions;

// Animation-related types
export interface ProjectCardAnimation {
  enter: () => void;
  leave: () => void;
  hover: () => void;
  unhover: () => void;
}

export interface PortfolioScrollTrigger {
  trigger: string;
  start: string;
  end: string;
  scrub?: boolean;
  pin?: boolean;
}

// Filter types
export type ProjectFilter = {
  key: keyof Project;
  value: any;
  operator: 'equals' | 'includes' | 'contains' | 'gt' | 'lt';
};

export type SortOption = {
  key: keyof Project;
  direction: 'asc' | 'desc';
};

// Event types
export interface PortfolioEvents {
  onProjectView: (project: Project) => void;
  onProjectShare: (project: Project) => void;
  onVideoPlay: (project: Project) => void;
  onFilterChange: (filters: PortfolioState['filters']) => void;
}
