/**
 * Lazy Loading Hook
 * React hook for managing component lazy loading and preloading
 */

import { useEffect, useRef, useCallback } from 'react';
import { bundleOptimizationService } from '@/services/bundleOptimizationService';

export interface LazyLoadingOptions {
  preloadOnHover?: boolean;
  preloadOnViewport?: boolean;
  preloadOnIdle?: boolean;
  viewportMargin?: string;
  hoverDelay?: number;
  idleDelay?: number;
}

export function useLazyLoading(
  chunkName: string,
  importFn: () => Promise<any>,
  options: LazyLoadingOptions = {}
) {
  const elementRef = useRef<HTMLElement>(null);
  const cleanupFnsRef = useRef<(() => void)[]>([]);

  const {
    preloadOnHover = false,
    preloadOnViewport = false,
    preloadOnIdle = false,
    viewportMargin = '50px',
    hoverDelay = 100,
    idleDelay = 1000,
  } = options;

  // Preload chunk manually
  const preload = useCallback(() => {
    bundleOptimizationService.preloadChunk(chunkName, importFn);
  }, [chunkName, importFn]);

  // Setup preloading strategies
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Clear previous cleanup functions
    cleanupFnsRef.current.forEach(cleanup => cleanup());
    cleanupFnsRef.current = [];

    // Hover preloading
    if (preloadOnHover) {
      const cleanup = bundleOptimizationService.preloadOnHover(element, chunkName, importFn);
      if (cleanup) cleanupFnsRef.current.push(cleanup);
    }

    // Viewport preloading
    if (preloadOnViewport) {
      const cleanup = bundleOptimizationService.preloadOnViewport(
        element,
        chunkName,
        importFn,
        { rootMargin: viewportMargin }
      );
      if (cleanup) cleanupFnsRef.current.push(cleanup);
    }

    // Idle preloading
    if (preloadOnIdle) {
      const timeoutId = setTimeout(() => {
        bundleOptimizationService.preloadOnIdle(chunkName, importFn);
      }, idleDelay);

      cleanupFnsRef.current.push(() => clearTimeout(timeoutId));
    }

    // Cleanup on unmount
    return () => {
      cleanupFnsRef.current.forEach(cleanup => cleanup());
    };
  }, [chunkName, importFn, preloadOnHover, preloadOnViewport, preloadOnIdle, viewportMargin, idleDelay]);

  return {
    elementRef,
    preload,
    isPreloaded: bundleOptimizationService.getPreloadStatus().preloaded.includes(chunkName),
    isLoading: bundleOptimizationService.getPreloadStatus().loading.includes(chunkName),
  };
}

/**
 * Hook for preloading components based on user navigation
 */
export function useNavigationPreloading() {
  const preloadedRoutes = useRef<Set<string>>(new Set());

  const preloadRoute = useCallback((routeName: string) => {
    if (preloadedRoutes.current.has(routeName)) return;

    preloadedRoutes.current.add(routeName);

    switch (routeName) {
      case 'portfolio':
        bundleOptimizationService.preloadChunk(
          'portfolio-section',
          () => import('@/features/portfolio')
        );
        break;
      case 'contact':
        bundleOptimizationService.preloadChunk(
          'contact-section',
          () => import('@/features/contact')
        );
        break;
      case 'about':
        bundleOptimizationService.preloadChunk(
          'about-section',
          () => import('@/features/about')
        );
        break;
    }
  }, []);

  const preloadOnNavigation = useCallback((targetRoute: string) => {
    // Preload the target route when user shows intent to navigate
    preloadRoute(targetRoute);
  }, [preloadRoute]);

  return {
    preloadRoute,
    preloadOnNavigation,
    preloadedRoutes: Array.from(preloadedRoutes.current),
  };
}

/**
 * Hook for monitoring bundle performance
 */
export function useBundlePerformance() {
  useEffect(() => {
    // Measure performance on mount
    bundleOptimizationService.measureBundlePerformance();

    // Enable development analysis
    if (process.env.NODE_ENV === 'development') {
      bundleOptimizationService.enableBundleAnalysis();
    }
  }, []);

  return {
    metrics: bundleOptimizationService.getBundleMetrics(),
    recommendations: bundleOptimizationService.getBundleRecommendations(),
    preloadStatus: bundleOptimizationService.getPreloadStatus(),
  };
}

/**
 * Hook for optimizing component loading based on device capabilities
 */
export function useAdaptiveLoading() {
  const getConnectionSpeed = useCallback(() => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      return connection.effectiveType || 'unknown';
    }
    return 'unknown';
  }, []);

  const getDeviceMemory = useCallback(() => {
    if ('deviceMemory' in navigator) {
      return (navigator as any).deviceMemory;
    }
    return 4; // Default assumption
  }, []);

  const shouldPreload = useCallback(() => {
    const connectionSpeed = getConnectionSpeed();
    const deviceMemory = getDeviceMemory();

    // Don't preload on slow connections or low-memory devices
    if (connectionSpeed === 'slow-2g' || connectionSpeed === '2g') {
      return false;
    }

    if (deviceMemory < 2) {
      return false;
    }

    return true;
  }, [getConnectionSpeed, getDeviceMemory]);

  const getOptimalStrategy = useCallback(() => {
    const connectionSpeed = getConnectionSpeed();
    const deviceMemory = getDeviceMemory();

    if (connectionSpeed === '4g' && deviceMemory >= 4) {
      return {
        preloadOnHover: true,
        preloadOnViewport: true,
        preloadOnIdle: true,
        priority: 'high' as const,
      };
    }

    if (connectionSpeed === '3g' && deviceMemory >= 2) {
      return {
        preloadOnHover: true,
        preloadOnViewport: false,
        preloadOnIdle: true,
        priority: 'medium' as const,
      };
    }

    return {
      preloadOnHover: false,
      preloadOnViewport: false,
      preloadOnIdle: false,
      priority: 'low' as const,
    };
  }, [getConnectionSpeed, getDeviceMemory]);

  return {
    connectionSpeed: getConnectionSpeed(),
    deviceMemory: getDeviceMemory(),
    shouldPreload: shouldPreload(),
    optimalStrategy: getOptimalStrategy(),
  };
}

export default useLazyLoading;
