/**
 * Spaceship-specific animation utilities and configurations
 * Provides reusable animation patterns for UFO and Rocket components
 */

import { gsap } from 'gsap';
import type { SpaceshipAnimationConfig, GSAPTimeline } from '@/types';
import { getAccessibilityConfig } from '@/utils';
import { 
  ANIMATION_DURATIONS, 
  ANIMATION_EASINGS, 
  COLOR_VARIANTS,
  CONTACT_CONFIG 
} from '@/constants';

/**
 * Spaceship flight path generators
 */
export const FLIGHT_PATHS = {
  linear: (startX: number, endX: number, y: number) => ({
    x: endX,
    y: y,
    ease: ANIMATION_EASINGS.POWER2_IN_OUT
  }),
  
  curved: (startX: number, endX: number, y: number) => ({
    motionPath: {
      path: `M${startX},${y} Q${(startX + endX) / 2},${y - 100} ${endX},${y}`,
      autoRotate: true
    },
    ease: ANIMATION_EASINGS.POWER2_IN_OUT
  }),
  
  'figure-8': (centerX: number, centerY: number, radius: number = 100) => ({
    motionPath: {
      path: `M${centerX - radius},${centerY} 
             C${centerX - radius},${centerY - radius} ${centerX + radius},${centerY - radius} ${centerX + radius},${centerY}
             C${centerX + radius},${centerY + radius} ${centerX - radius},${centerY + radius} ${centerX - radius},${centerY}`,
      autoRotate: true
    },
    ease: "none"
  }),
  
  circular: (centerX: number, centerY: number, radius: number = 80) => ({
    motionPath: {
      path: `M${centerX + radius},${centerY} 
             A${radius},${radius} 0 1,1 ${centerX + radius},${centerY}`,
      autoRotate: true
    },
    ease: "none"
  })
} as const;

/**
 * Spaceship hover animations
 */
export const HOVER_ANIMATIONS = {
  ufo: {
    enter: {
      scale: 1.1,
      y: -5,
      filter: `drop-shadow(0 0 20px ${COLOR_VARIANTS.BLUE})`,
      duration: ANIMATION_DURATIONS.FAST,
      ease: ANIMATION_EASINGS.POWER2_OUT
    },
    leave: {
      scale: 1,
      y: 0,
      filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))',
      duration: ANIMATION_DURATIONS.FAST,
      ease: ANIMATION_EASINGS.POWER2_OUT
    }
  },
  
  rocket: {
    enter: {
      scale: 1.1,
      y: -5,
      filter: `drop-shadow(0 0 20px ${COLOR_VARIANTS.PINK})`,
      duration: ANIMATION_DURATIONS.FAST,
      ease: ANIMATION_EASINGS.POWER2_OUT
    },
    leave: {
      scale: 1,
      y: 0,
      filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))',
      duration: ANIMATION_DURATIONS.FAST,
      ease: ANIMATION_EASINGS.POWER2_OUT
    }
  }
} as const;

/**
 * Spaceship floating animations
 */
export const FLOATING_ANIMATIONS = {
  gentle: {
    y: CONTACT_CONFIG.SPACESHIP_FLOAT_AMPLITUDE,
    rotation: 3,
    duration: 3,
    ease: "power2.inOut",
    repeat: -1,
    yoyo: true
  },
  
  active: {
    y: CONTACT_CONFIG.SPACESHIP_FLOAT_AMPLITUDE * 1.5,
    rotation: 5,
    duration: 2,
    ease: "power2.inOut",
    repeat: -1,
    yoyo: true
  },
  
  subtle: {
    y: CONTACT_CONFIG.SPACESHIP_FLOAT_AMPLITUDE * 0.5,
    rotation: 1,
    duration: 4,
    ease: "power1.inOut",
    repeat: -1,
    yoyo: true
  }
} as const;

/**
 * Spaceship entrance animations
 */
export const ENTRANCE_ANIMATIONS = {
  flyInLeft: (config: SpaceshipAnimationConfig = {}) => ({
    from: {
      x: -250,
      y: 60,
      rotation: 20,
      opacity: 0,
      scale: 0.8
    },
    to: {
      x: 0,
      y: 0,
      rotation: 0,
      opacity: 1,
      scale: 1,
      duration: config.duration || CONTACT_CONFIG.SPACESHIP_FLIGHT_DURATION,
      ease: config.ease || ANIMATION_EASINGS.POWER2_OUT
    }
  }),
  
  flyInRight: (config: SpaceshipAnimationConfig = {}) => ({
    from: {
      x: 250,
      y: -60,
      rotation: -20,
      opacity: 0,
      scale: 0.8
    },
    to: {
      x: 0,
      y: 0,
      rotation: 0,
      opacity: 1,
      scale: 1,
      duration: config.duration || CONTACT_CONFIG.SPACESHIP_FLIGHT_DURATION,
      ease: config.ease || ANIMATION_EASINGS.POWER2_OUT
    }
  }),
  
  flyInTop: (config: SpaceshipAnimationConfig = {}) => ({
    from: {
      x: 0,
      y: -200,
      rotation: 0,
      opacity: 0,
      scale: 0.8
    },
    to: {
      x: 0,
      y: 0,
      rotation: 0,
      opacity: 1,
      scale: 1,
      duration: config.duration || CONTACT_CONFIG.SPACESHIP_FLIGHT_DURATION,
      ease: config.ease || ANIMATION_EASINGS.POWER2_OUT
    }
  })
} as const;

/**
 * Laser and beam animations
 */
export const LASER_ANIMATIONS = {
  trace: {
    from: {
      opacity: 0,
      scaleY: 0,
      transformOrigin: "top center"
    },
    to: {
      opacity: 1,
      scaleY: 1,
      duration: CONTACT_CONFIG.LASER_TRACE_DURATION,
      ease: ANIMATION_EASINGS.POWER2_OUT
    }
  },
  
  pulse: {
    opacity: 0.8,
    scaleX: 1.1,
    duration: 0.5,
    ease: "power2.inOut",
    repeat: -1,
    yoyo: true
  },
  
  fadeOut: {
    opacity: 0,
    scaleY: 0,
    duration: 0.5,
    ease: ANIMATION_EASINGS.POWER2_IN
  }
} as const;

/**
 * Creates a spaceship flight animation
 */
export function createFlightAnimation(
  spaceship: Element,
  config: SpaceshipAnimationConfig
): any {
  const accessibility = getAccessibilityConfig();
  
  if (accessibility.reduceMotion) {
    // Set final position immediately
    gsap.set(spaceship, { opacity: 1, x: 0, y: 0, rotation: 0, scale: 1 });
    return null;
  }

  const timeline = gsap.timeline();
  const { flightPath = 'linear', direction = 'left-to-right' } = config;
  
  // Get spaceship dimensions and screen size
  const rect = spaceship.getBoundingClientRect();
  const screenWidth = window.innerWidth;
  const screenHeight = window.innerHeight;
  
  // Calculate start and end positions based on direction
  let startX: number, endX: number, y: number;
  
  switch (direction) {
    case 'left-to-right':
      startX = -rect.width;
      endX = screenWidth + rect.width;
      y = screenHeight * 0.3;
      break;
    case 'right-to-left':
      startX = screenWidth + rect.width;
      endX = -rect.width;
      y = screenHeight * 0.3;
      break;
    case 'top-to-bottom':
      startX = screenWidth * 0.5;
      endX = screenWidth * 0.5;
      y = screenHeight + rect.height;
      break;
    case 'bottom-to-top':
      startX = screenWidth * 0.5;
      endX = screenWidth * 0.5;
      y = -rect.height;
      break;
    default:
      startX = -rect.width;
      endX = screenWidth + rect.width;
      y = screenHeight * 0.3;
  }
  
  // Set initial position
  timeline.set(spaceship, {
    x: startX,
    y: y,
    opacity: 1,
    scale: 1
  });
  
  // Create flight path animation
  const pathAnimation = FLIGHT_PATHS[flightPath](startX, endX, y);
  timeline.to(spaceship, {
    ...pathAnimation,
    duration: config.duration || ANIMATION_DURATIONS.VERY_SLOW,
    ease: config.ease || ANIMATION_EASINGS.POWER2_IN_OUT
  });
  
  return timeline;
}

/**
 * Creates a spaceship hover effect
 */
export function createHoverEffect(
  spaceship: Element,
  type: 'ufo' | 'rocket' = 'ufo'
): { enter: () => void; leave: () => void } {
  const accessibility = getAccessibilityConfig();
  
  if (accessibility.reduceMotion) {
    return {
      enter: () => {},
      leave: () => {}
    };
  }
  
  const animations = HOVER_ANIMATIONS[type];
  
  return {
    enter: () => gsap.to(spaceship, animations.enter),
    leave: () => gsap.to(spaceship, animations.leave)
  };
}

/**
 * Creates a spaceship floating animation
 */
export function createFloatingAnimation(
  spaceship: Element,
  intensity: 'gentle' | 'active' | 'subtle' = 'gentle'
): any {
  const accessibility = getAccessibilityConfig();
  
  if (accessibility.reduceMotion) {
    return null;
  }
  
  const animation = FLOATING_ANIMATIONS[intensity];
  return gsap.to(spaceship, animation);
}

/**
 * Creates a laser beam animation
 */
export function createLaserAnimation(
  beam: Element,
  type: 'trace' | 'pulse' | 'fadeOut' = 'trace'
): any {
  const accessibility = getAccessibilityConfig();

  if (accessibility.reduceMotion) {
    if (type === 'trace') {
      gsap.set(beam, { opacity: 1, scaleY: 1 });
    }
    return null;
  }

  const animation = LASER_ANIMATIONS[type];

  if (type === 'trace' && 'from' in animation && 'to' in animation) {
    return gsap.fromTo(beam, animation.from, animation.to);
  } else {
    return gsap.to(beam, animation);
  }
}

/**
 * Creates a complex spaceship sequence (entrance + floating + exit)
 */
export function createSpaceshipSequence(
  spaceship: Element,
  config: SpaceshipAnimationConfig & {
    entranceType?: keyof typeof ENTRANCE_ANIMATIONS;
    floatingIntensity?: keyof typeof FLOATING_ANIMATIONS;
    exitDelay?: number;
  }
): any {
  const accessibility = getAccessibilityConfig();
  
  if (accessibility.reduceMotion) {
    gsap.set(spaceship, { opacity: 1, x: 0, y: 0, rotation: 0, scale: 1 });
    return null;
  }
  
  const {
    entranceType = 'flyInLeft',
    floatingIntensity = 'gentle',
    exitDelay = 5,
    ...animationConfig
  } = config;
  
  const timeline = gsap.timeline();
  const entrance = ENTRANCE_ANIMATIONS[entranceType](animationConfig);
  
  // Entrance animation
  timeline.fromTo(spaceship, entrance.from, entrance.to);
  
  // Floating animation
  timeline.to(spaceship, FLOATING_ANIMATIONS[floatingIntensity], `+=${exitDelay}`);
  
  return timeline;
}
