/**
 * Project card component with hover animations and video support
 */

import React, { useRef, useCallback } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { ANIMATION_DURATIONS, ANIMATION_EASINGS } from '@/constants';
import type { ProjectData } from '@/types';
import VideoPlayer from './VideoPlayer';

interface ProjectCardProps {
  project: ProjectData;
  index: number;
  onVideoClick?: (videoSrc: string, title: string) => void;
  className?: string;
}

const ProjectCard = React.forwardRef<HTMLDivElement, ProjectCardProps>(({
  project,
  index,
  onVideoClick,
  className = ''
}, ref) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  const handleCardHover = useCallback((isEntering: boolean) => {
    if (!cardRef.current || !gsap || !isAvailable) return;

    try {
      const accessibility = getAccessibilityConfig();
      
      if (accessibility.reduceMotion) {
        // Subtle changes for reduced motion
        if (isEntering) {
          gsap.to(cardRef.current, {
            scale: 1.01,
            duration: ANIMATION_DURATIONS.FAST,
            ease: ANIMATION_EASINGS.POWER2_OUT
          });
        } else {
          gsap.to(cardRef.current, {
            scale: 1,
            duration: ANIMATION_DURATIONS.FAST,
            ease: ANIMATION_EASINGS.POWER2_OUT
          });
        }
        return;
      }

      if (isEntering) {
        gsap.to(cardRef.current, {
          y: -10,
          scale: 1.02,
          boxShadow: `0 20px 40px rgba(255, 51, 102, 0.3), 0 0 30px rgba(74, 144, 226, 0.2)`,
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT
        });
      } else {
        gsap.to(cardRef.current, {
          y: 0,
          scale: 1,
          boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)",
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT
        });
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [gsap, isAvailable, handleError]);

  const handleVideoClick = useCallback(() => {
    if (project.hasVideo && onVideoClick) {
      onVideoClick('/portfolio-video-prototype.mp4', project.title);
    }
  }, [project.hasVideo, project.title, onVideoClick]);

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    // Hide broken images gracefully
    const target = e.target as HTMLImageElement;
    target.style.display = 'none';
  };

  return (
    <div
      ref={ref || cardRef}
      className={`group relative bg-[#0F0F0F] rounded-lg overflow-hidden border border-gray-800 ${className}`}
      style={{
        boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)",
        willChange: "transform"
      }}
      onMouseEnter={() => handleCardHover(true)}
      onMouseLeave={() => handleCardHover(false)}
      role="article"
      aria-labelledby={`project-title-${project.id}`}
    >
      {/* Project Media */}
      <div className="relative w-full h-64 md:h-72 overflow-hidden">
        {project.hasVideo ? (
          <VideoPlayer
            src="/portfolio-video-prototype.mp4"
            poster={project.placeholder}
            onClick={handleVideoClick}
            className="transition-transform duration-500 group-hover:scale-110"
          />
        ) : (
          <div className="relative w-full h-full">
            <img
              src={project.placeholder}
              alt={project.title}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              onError={handleImageError}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-[#0F0F0F] via-transparent to-transparent opacity-60" />
          </div>
        )}

        {/* Status Badge */}
        <div className="absolute top-4 left-4">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#FF3366] text-white">
            {project.status === 'coming-soon' ? 'Coming Soon' : 
             project.status === 'live' ? 'Live' : 'In Development'}
          </span>
        </div>
      </div>

      {/* Project Info */}
      <div className="p-6">
        <div className="mb-3">
          <span className="text-[#4A90E2] text-sm font-medium uppercase tracking-wider">
            {project.type}
          </span>
        </div>
        
        <h3
          id={`project-title-${project.id}`}
          className="text-xl md:text-2xl font-bold text-white mb-3 group-hover:text-[#FF3366] transition-colors duration-300"
        >
          {project.title}
        </h3>
        
        <p className="text-gray-400 leading-relaxed mb-4">
          {project.description}
        </p>

        {/* Technologies */}
        {project.technologies && project.technologies.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {project.technologies.map((tech, techIndex) => (
              <span
                key={techIndex}
                className="px-2 py-1 bg-[#1A1A1A] text-gray-300 text-xs rounded border border-gray-700"
              >
                {tech}
              </span>
            ))}
          </div>
        )}

        {/* Action Links */}
        <div className="flex items-center space-x-4">
          {project.liveUrl && (
            <a
              href={project.liveUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-[#4A90E2] hover:text-[#FF3366] transition-colors duration-300 text-sm font-medium"
            >
              <span className="mr-1">🔗</span>
              Live Demo
            </a>
          )}
          
          {project.githubUrl && (
            <a
              href={project.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-[#4A90E2] hover:text-[#FF3366] transition-colors duration-300 text-sm font-medium"
            >
              <span className="mr-1">📁</span>
              Source Code
            </a>
          )}

          {project.hasVideo && (
            <button
              onClick={handleVideoClick}
              className="inline-flex items-center text-[#4A90E2] hover:text-[#FF3366] transition-colors duration-300 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-[#FF3366] focus:ring-offset-2 focus:ring-offset-[#0F0F0F] rounded"
            >
              <span className="mr-1">▶️</span>
              Watch Demo
            </button>
          )}
        </div>
      </div>
    </div>
  );
});

ProjectCard.displayName = 'ProjectCard';

export default ProjectCard;
