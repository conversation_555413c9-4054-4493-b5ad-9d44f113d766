/**
 * Base spaceship component providing shared functionality and structure
 */

import React, { useRef, useEffect, useCallback, useState } from 'react';
import { useSpaceshipAnimation } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import type {
  AnimatedSpaceshipProps,
  SpaceshipConfig,
  SpaceshipRefs,
  SpaceshipAnimationContext,
  SpaceshipEventHandlers
} from './types';
import { SPACESHIP_SIZE_CONFIGS } from './types';

interface BaseSpaceshipProps extends AnimatedSpaceshipProps {
  config: SpaceshipConfig;
  children: (props: {
    refs: SpaceshipRefs;
    config: SpaceshipConfig;
    sizeConfig: any;
    animationContext: SpaceshipAnimationContext;
  }) => React.ReactNode;
}

export default function BaseSpaceship({
  className = '',
  size = 'md',
  variant = 'default',
  enableHover = true,
  enableFloating = false,
  floatingIntensity = 'gentle',
  animationConfig,
  onClick,
  onHover,
  onAnimationComplete = () => {},
  onAnimationStart = () => {},
  config,
  children
}: BaseSpaceshipProps) {
  // Refs for spaceship elements
  const containerRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const beamRef = useRef<SVGGElement>(null);
  const glowRef = useRef<SVGGElement>(null);
  const lightsRef = useRef<SVGGElement>(null);
  const exhaustRef = useRef<SVGGElement>(null);

  // Animation state
  const [animationContext, setAnimationContext] = useState<SpaceshipAnimationContext>({
    isHovering: false,
    isFloating: false,
    isFlying: false,
    animationQueue: []
  });

  // Hooks
  const {
    createHoverEffect,
    createFloatingAnimation,
    isAvailable
  } = useSpaceshipAnimation();
  const { handleError } = useErrorHandler();

  // Get size configuration
  const sizeConfig = SPACESHIP_SIZE_CONFIGS[size];

  // Create refs object
  const refs: SpaceshipRefs = {
    container: containerRef,
    svg: svgRef,
    beam: beamRef,
    glow: glowRef,
    lights: lightsRef,
    exhaust: exhaustRef
  };

  // Hover effect handlers
  const hoverEffect = useRef<{ enter: () => void; leave: () => void } | null>(null);

  // Initialize hover effects
  useEffect(() => {
    if (!enableHover || !isAvailable || !svgRef.current) return;

    try {
      hoverEffect.current = createHoverEffect(svgRef.current, config.type);
    } catch (error) {
      handleError(error as Error);
    }
  }, [enableHover, isAvailable, createHoverEffect, config.type, handleError]);

  // Initialize floating animation
  useEffect(() => {
    if (!enableFloating || !isAvailable || !svgRef.current) return;

    try {
      const floatingAnimation = createFloatingAnimation(svgRef.current, floatingIntensity);
      
      if (floatingAnimation) {
        setAnimationContext(prev => ({ ...prev, isFloating: true }));
        
        return () => {
          if (floatingAnimation.kill) {
            floatingAnimation.kill();
          }
          setAnimationContext(prev => ({ ...prev, isFloating: false }));
        };
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [enableFloating, isAvailable, createFloatingAnimation, floatingIntensity, handleError]);

  // Handle mouse enter
  const handleMouseEnter = useCallback(() => {
    try {
      if (hoverEffect.current && !animationContext.isHovering) {
        hoverEffect.current.enter();
        setAnimationContext(prev => ({ ...prev, isHovering: true }));
        onHover?.(true);
        onAnimationStart?.();
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [animationContext.isHovering, onHover, onAnimationStart, handleError]);

  // Handle mouse leave
  const handleMouseLeave = useCallback(() => {
    try {
      if (hoverEffect.current && animationContext.isHovering) {
        hoverEffect.current.leave();
        setAnimationContext(prev => ({ ...prev, isHovering: false }));
        onHover?.(false);
        onAnimationComplete?.();
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [animationContext.isHovering, onHover, onAnimationComplete, handleError]);

  // Handle click
  const handleClick = useCallback(() => {
    try {
      onClick?.();
      onAnimationStart?.();

      // Add click animation logic here if needed
      setTimeout(() => {
        onAnimationComplete?.();
      }, 300);
    } catch (error) {
      handleError(error as Error);
    }
  }, [onClick, onAnimationStart, onAnimationComplete, handleError]);

  // Accessibility considerations
  const accessibility = getAccessibilityConfig();
  const shouldReduceMotion = accessibility.reduceMotion;

  // Event handlers object
  const eventHandlers: SpaceshipEventHandlers = {
    onMouseEnter: enableHover && !shouldReduceMotion ? handleMouseEnter : undefined,
    onMouseLeave: enableHover && !shouldReduceMotion ? handleMouseLeave : undefined,
    onClick: handleClick,
    onAnimationStart,
    onAnimationComplete
  };

  return (
    <div
      ref={containerRef}
      className={`inline-block ${className}`}
      style={{ 
        filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))',
        cursor: onClick ? 'pointer' : 'default'
      }}
      onMouseEnter={eventHandlers.onMouseEnter}
      onMouseLeave={eventHandlers.onMouseLeave}
      onClick={eventHandlers.onClick}
      role={onClick ? 'button' : 'img'}
      tabIndex={onClick ? 0 : -1}
      aria-label={`${config.type} spaceship`}
      onKeyDown={(e) => {
        if (onClick && (e.key === 'Enter' || e.key === ' ')) {
          e.preventDefault();
          handleClick();
        }
      }}
    >
      {children({
        refs,
        config,
        sizeConfig,
        animationContext
      })}
    </div>
  );
}

// Higher-order component for creating spaceship variants
export function withSpaceshipAnimation<T extends AnimatedSpaceshipProps>(
  WrappedComponent: React.ComponentType<T>,
  defaultConfig: SpaceshipConfig
) {
  return function AnimatedSpaceshipComponent(props: T) {
    return (
      <BaseSpaceship
        {...props}
        config={defaultConfig}
      >
        {({ refs, config, sizeConfig, animationContext }) => (
          <WrappedComponent
            {...props}
            refs={refs}
            config={config}
            sizeConfig={sizeConfig}
            animationContext={animationContext}
          />
        )}
      </BaseSpaceship>
    );
  };
}

// Utility hook for spaceship component logic
export function useSpaceshipComponent(config: SpaceshipConfig) {
  const [isHovering, setIsHovering] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const { handleError } = useErrorHandler();

  const handleAnimationStart = useCallback((animationName: string) => {
    setIsAnimating(true);
  }, []);

  const handleAnimationComplete = useCallback((animationName: string) => {
    setIsAnimating(false);
  }, []);

  const handleHover = useCallback((hovering: boolean) => {
    setIsHovering(hovering);
  }, []);

  return {
    isHovering,
    isAnimating,
    handleAnimationStart,
    handleAnimationComplete,
    handleHover,
    handleError
  };
}
