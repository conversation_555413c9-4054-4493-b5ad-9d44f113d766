/**
 * Spaceship component types and interfaces
 */

import type { 
  BaseSpaceshipProps, 
  SpaceshipSize, 
  SpaceshipType, 
  SpaceshipVariant,
  SpaceshipSizeConfig,
  SpaceshipAnimationState,
  SpaceshipAnimationConfig
} from '@/types';

// Re-export base types for convenience
export type {
  BaseSpaceshipProps,
  SpaceshipSize,
  SpaceshipType,
  SpaceshipVariant,
  SpaceshipSizeConfig,
  SpaceshipAnimationState,
  SpaceshipAnimationConfig
};

// Extended spaceship props with animation capabilities
export interface AnimatedSpaceshipProps extends BaseSpaceshipProps {
  enableHover?: boolean;
  enableFloating?: boolean;
  floatingIntensity?: 'gentle' | 'active' | 'subtle';
  animationConfig?: SpaceshipAnimationConfig;
  onAnimationComplete?: () => void;
  onAnimationStart?: () => void;
}

// Spaceship component configuration
export interface SpaceshipConfig {
  type: SpaceshipType;
  size: SpaceshipSize;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    glow: string;
  };
  gradients: {
    body: string;
    dome: string;
    beam: string;
  };
  animations: {
    hover: SpaceshipAnimationConfig;
    floating: SpaceshipAnimationConfig;
    entrance: SpaceshipAnimationConfig;
  };
}

// Spaceship SVG element refs
export interface SpaceshipRefs {
  container: React.RefObject<HTMLDivElement>;
  svg: React.RefObject<SVGSVGElement>;
  beam?: React.RefObject<SVGGElement>;
  glow?: React.RefObject<SVGGElement>;
  lights?: React.RefObject<SVGGElement>;
  exhaust?: React.RefObject<SVGGElement>;
}

// Spaceship animation context
export interface SpaceshipAnimationContext {
  isHovering: boolean;
  isFloating: boolean;
  isFlying: boolean;
  currentAnimation?: string;
  animationQueue: string[];
}

// Spaceship event handlers
export interface SpaceshipEventHandlers {
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onClick?: () => void;
  onAnimationStart?: (animationName: string) => void;
  onAnimationComplete?: (animationName: string) => void;
}

// Size configuration mapping
export const SPACESHIP_SIZE_CONFIGS: Record<SpaceshipSize, SpaceshipSizeConfig> = {
  sm: { width: 56, height: 48, viewBox: '0 0 56 48' },
  md: { width: 72, height: 60, viewBox: '0 0 72 60' },
  lg: { width: 88, height: 72, viewBox: '0 0 88 72' }
};

// Default spaceship configurations
export const DEFAULT_UFO_CONFIG: SpaceshipConfig = {
  type: 'ufo',
  size: 'md',
  colors: {
    primary: '#4A90E2',
    secondary: '#2563EB',
    accent: '#60A5FA',
    glow: '#4A90E2'
  },
  gradients: {
    body: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%)',
    dome: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
    beam: 'linear-gradient(to bottom, rgba(74, 144, 226, 0.8) 0%, rgba(74, 144, 226, 0.2) 100%)'
  },
  animations: {
    hover: {
      duration: 0.3,
      ease: 'power2.out',
      hasGlow: true
    },
    floating: {
      duration: 3,
      ease: 'power2.inOut',
      repeat: -1,
      yoyo: true
    },
    entrance: {
      duration: 2,
      ease: 'power2.out',
      direction: 'left-to-right'
    }
  }
};

export const DEFAULT_ROCKET_CONFIG: SpaceshipConfig = {
  type: 'rocket',
  size: 'md',
  colors: {
    primary: '#FF3366',
    secondary: '#FF1A4D',
    accent: '#FF6B8A',
    glow: '#FF3366'
  },
  gradients: {
    body: 'linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f87171 100%)',
    dome: 'linear-gradient(135deg, #b91c1c 0%, #dc2626 100%)',
    beam: 'linear-gradient(to bottom, rgba(255, 51, 102, 0.8) 0%, rgba(255, 51, 102, 0.2) 100%)'
  },
  animations: {
    hover: {
      duration: 0.3,
      ease: 'power2.out',
      hasGlow: true
    },
    floating: {
      duration: 2.5,
      ease: 'power2.inOut',
      repeat: -1,
      yoyo: true
    },
    entrance: {
      duration: 2,
      ease: 'power2.out',
      direction: 'right-to-left'
    }
  }
};

// Utility function to get spaceship configuration
export function getSpaceshipConfig(type: SpaceshipType): SpaceshipConfig {
  return type === 'ufo' ? DEFAULT_UFO_CONFIG : DEFAULT_ROCKET_CONFIG;
}

// Utility function to merge spaceship configurations
export function mergeSpaceshipConfig(
  base: SpaceshipConfig,
  overrides: Partial<SpaceshipConfig>
): SpaceshipConfig {
  return {
    ...base,
    ...overrides,
    colors: { ...base.colors, ...overrides.colors },
    gradients: { ...base.gradients, ...overrides.gradients },
    animations: { ...base.animations, ...overrides.animations }
  };
}
