/**
 * Portfolio grid component with staggered animations
 */

import React, { useRef, useEffect } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { ANIMATION_DURATIONS, ANIMATION_EASINGS, PROJECTS } from '@/constants';
import type { ProjectData } from '@/types';
import ProjectCard from './ProjectCard';

interface PortfolioGridProps {
  projects?: ProjectData[];
  onVideoClick?: (videoSrc: string, title: string) => void;
  autoAnimate?: boolean;
  animationDelay?: number;
  className?: string;
}

export default function PortfolioGrid({
  projects = PROJECTS,
  onVideoClick,
  autoAnimate = false,
  animationDelay = 0,
  className = ''
}: PortfolioGridProps) {
  const gridRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<HTMLDivElement[]>([]);
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  // Initialize card animations
  useEffect(() => {
    if (!autoAnimate || !isAvailable || !gsap) return;

    const timer = setTimeout(() => {
      try {
        const accessibility = getAccessibilityConfig();

        if (accessibility.reduceMotion) {
          // Set final states immediately for reduced motion
          cardsRef.current.forEach((card) => {
            if (card) {
              gsap.set(card, { opacity: 1, y: 0, scale: 1 });
            }
          });
          return;
        }

        // Animate project cards with scroll triggers and stagger
        cardsRef.current.forEach((card, index) => {
          if (card) {
            gsap.fromTo(card,
              { 
                opacity: 0, 
                y: 80, 
                scale: 0.9 
              },
              {
                opacity: 1,
                y: 0,
                scale: 1,
                duration: ANIMATION_DURATIONS.SLOW,
                delay: index * 0.2,
                ease: ANIMATION_EASINGS.POWER2_OUT,
                scrollTrigger: {
                  trigger: card,
                  start: "top 85%",
                  end: "bottom 20%",
                  toggleActions: "play none none reverse"
                }
              }
            );
          }
        });
      } catch (error) {
        handleError(error as Error);
      }
    }, animationDelay);

    return () => clearTimeout(timer);
  }, [autoAnimate, isAvailable, gsap, animationDelay, handleError]);

  // Set card refs
  const setCardRef = (el: HTMLDivElement | null, index: number) => {
    if (el) {
      cardsRef.current[index] = el;
    }
  };

  return (
    <div
      ref={gridRef}
      className={`grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12 ${className}`}
    >
      {projects.map((project, index) => (
        <ProjectCard
          key={project.id}
          project={project}
          index={index}
          onVideoClick={onVideoClick}
          className=""
          ref={(el) => setCardRef(el, index)}
        />
      ))}
    </div>
  );
}

// Enhanced version with custom project filtering
interface FilterablePortfolioGridProps extends PortfolioGridProps {
  filterBy?: 'status' | 'type' | 'technology';
  filterValue?: string;
  showFilters?: boolean;
}

export function FilterablePortfolioGrid({
  projects = PROJECTS,
  filterBy,
  filterValue,
  showFilters = false,
  ...props
}: FilterablePortfolioGridProps) {
  const [filteredProjects, setFilteredProjects] = React.useState(projects);
  const [activeFilter, setActiveFilter] = React.useState<string>('all');

  // Filter projects based on criteria
  React.useEffect(() => {
    if (!filterBy || !filterValue || filterValue === 'all') {
      setFilteredProjects(projects);
      return;
    }

    const filtered = projects.filter(project => {
      switch (filterBy) {
        case 'status':
          return project.status === filterValue;
        case 'type':
          return project.type.toLowerCase().includes(filterValue.toLowerCase());
        case 'technology':
          return project.technologies?.some(tech => 
            tech.toLowerCase().includes(filterValue.toLowerCase())
          );
        default:
          return true;
      }
    });

    setFilteredProjects(filtered);
  }, [projects, filterBy, filterValue]);

  // Get unique filter options
  const getFilterOptions = () => {
    if (!filterBy) return [];

    switch (filterBy) {
      case 'status':
        return Array.from(new Set(projects.map(p => p.status)));
      case 'type':
        return Array.from(new Set(projects.map(p => p.type)));
      case 'technology':
        return Array.from(new Set(projects.flatMap(p => p.technologies || [])));
      default:
        return [];
    }
  };

  const filterOptions = getFilterOptions();

  return (
    <div>
      {/* Filter Controls */}
      {showFilters && filterOptions.length > 0 && (
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 justify-center">
            <button
              onClick={() => setActiveFilter('all')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                activeFilter === 'all'
                  ? 'bg-[#FF3366] text-white'
                  : 'bg-[#1A1A1A] text-gray-400 hover:text-white hover:bg-[#2A2A2A]'
              }`}
            >
              All Projects
            </button>
            {filterOptions.map(option => (
              <button
                key={option}
                onClick={() => setActiveFilter(option)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 capitalize ${
                  activeFilter === option
                    ? 'bg-[#FF3366] text-white'
                    : 'bg-[#1A1A1A] text-gray-400 hover:text-white hover:bg-[#2A2A2A]'
                }`}
              >
                {option.replace('-', ' ')}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Project Grid */}
      <PortfolioGrid
        {...props}
        projects={filteredProjects}
      />

      {/* Empty State */}
      {filteredProjects.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400 text-lg">No projects found matching the current filter.</p>
          <button
            onClick={() => setActiveFilter('all')}
            className="mt-4 text-[#FF3366] hover:text-[#FF1A4D] transition-colors duration-300"
          >
            Show all projects
          </button>
        </div>
      )}
    </div>
  );
}
