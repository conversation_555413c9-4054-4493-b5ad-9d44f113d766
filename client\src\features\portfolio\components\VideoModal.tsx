/**
 * Video modal component with GSAP animations
 */

import React, { useRef, useEffect } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { ANIMATION_DURATIONS, ANIMATION_EASINGS, Z_INDEX } from '@/constants';

interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoSrc: string;
  title: string;
}

export default function VideoModal({
  isOpen,
  onClose,
  videoSrc,
  title
}: VideoModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Animation effects
  useEffect(() => {
    if (!isAvailable || !gsap || !modalRef.current || !overlayRef.current || !contentRef.current) return;

    const accessibility = getAccessibilityConfig();

    try {
      if (isOpen) {
        // Show modal
        gsap.set(modalRef.current, { display: 'flex' });

        if (accessibility.reduceMotion) {
          // Immediate show for reduced motion
          gsap.set([overlayRef.current, contentRef.current], { opacity: 1 });
        } else {
          // Animated entrance
          const timeline = gsap.timeline();
          
          timeline
            .set([overlayRef.current, contentRef.current], { opacity: 0 })
            .to(overlayRef.current, {
              opacity: 1,
              duration: ANIMATION_DURATIONS.FAST,
              ease: ANIMATION_EASINGS.POWER2_OUT
            })
            .to(contentRef.current, {
              opacity: 1,
              scale: 1,
              y: 0,
              duration: ANIMATION_DURATIONS.NORMAL,
              ease: ANIMATION_EASINGS.BACK_OUT
            }, '-=0.1');
        }

        // Start video playback
        if (videoRef.current) {
          videoRef.current.play().catch((error) => {
            console.warn('Video autoplay failed in modal:', error);
          });
        }
      } else {
        // Hide modal
        if (accessibility.reduceMotion) {
          // Immediate hide for reduced motion
          gsap.set(modalRef.current, { display: 'none' });
        } else {
          // Animated exit
          const timeline = gsap.timeline({
            onComplete: () => {
              if (modalRef.current) {
                gsap.set(modalRef.current, { display: 'none' });
              }
            }
          });

          timeline
            .to(contentRef.current, {
              opacity: 0,
              scale: 0.8,
              y: 50,
              duration: ANIMATION_DURATIONS.FAST,
              ease: ANIMATION_EASINGS.POWER2_IN
            })
            .to(overlayRef.current, {
              opacity: 0,
              duration: ANIMATION_DURATIONS.FAST,
              ease: ANIMATION_EASINGS.POWER2_IN
            }, '-=0.1');
        }

        // Pause video
        if (videoRef.current) {
          videoRef.current.pause();
          videoRef.current.currentTime = 0;
        }
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [isOpen, isAvailable, gsap, handleError]);

  // Initialize modal as hidden
  useEffect(() => {
    if (isAvailable && gsap && modalRef.current) {
      gsap.set(modalRef.current, { display: 'none' });
      if (contentRef.current) {
        gsap.set(contentRef.current, { scale: 0.8, y: 50 });
      }
    }
  }, [isAvailable, gsap]);

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleCloseClick = () => {
    onClose();
  };

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 flex items-center justify-center p-4"
      style={{ zIndex: Z_INDEX.MODAL }}
      role="dialog"
      aria-modal="true"
      aria-labelledby="video-modal-title"
    >
      {/* Overlay */}
      <div
        ref={overlayRef}
        className="absolute inset-0 bg-black bg-opacity-80 backdrop-blur-sm"
        onClick={handleOverlayClick}
      />

      {/* Modal Content */}
      <div
        ref={contentRef}
        className="relative w-full max-w-4xl aspect-video bg-black rounded-lg overflow-hidden shadow-2xl"
      >
        {/* Close Button */}
        <button
          onClick={handleCloseClick}
          className="absolute top-4 right-4 z-10 w-10 h-10 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full flex items-center justify-center text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#FF3366] focus:ring-offset-2 focus:ring-offset-black"
          aria-label="Close video modal"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18" />
            <line x1="6" y1="6" x2="18" y2="18" />
          </svg>
        </button>

        {/* Video Title */}
        <div className="absolute top-4 left-4 z-10">
          <h3
            id="video-modal-title"
            className="text-white text-lg font-semibold bg-black bg-opacity-50 px-3 py-1 rounded"
          >
            {title}
          </h3>
        </div>

        {/* Video */}
        <video
          ref={videoRef}
          src={videoSrc}
          className="w-full h-full object-cover"
          controls
          muted
          loop
          playsInline
          preload="metadata"
        />
      </div>
    </div>
  );
}
