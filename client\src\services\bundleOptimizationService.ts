/**
 * Bundle Optimization Service
 * Utilities for managing code splitting and bundle optimization
 */

export interface BundleMetrics {
  initialBundleSize: number;
  lazyChunks: string[];
  totalChunks: number;
  compressionRatio: number;
  loadTime: number;
}

export interface PreloadStrategy {
  onHover: boolean;
  onViewport: boolean;
  onIdle: boolean;
  priority: 'high' | 'medium' | 'low';
}

class BundleOptimizationService {
  private preloadedChunks: Set<string> = new Set();
  private loadingChunks: Set<string> = new Set();
  private metrics: Partial<BundleMetrics> = {};

  /**
   * Preload a component chunk
   */
  async preloadChunk(chunkName: string, importFn: () => Promise<any>): Promise<void> {
    if (this.preloadedChunks.has(chunkName) || this.loadingChunks.has(chunkName)) {
      return;
    }

    this.loadingChunks.add(chunkName);
    
    try {
      const startTime = performance.now();
      await importFn();
      const loadTime = performance.now() - startTime;
      
      this.preloadedChunks.add(chunkName);
      this.loadingChunks.delete(chunkName);
      
      console.log(`Preloaded chunk "${chunkName}" in ${loadTime.toFixed(2)}ms`);
    } catch (error) {
      this.loadingChunks.delete(chunkName);
      console.error(`Failed to preload chunk "${chunkName}":`, error);
    }
  }

  /**
   * Preload component on hover
   */
  preloadOnHover(element: HTMLElement, chunkName: string, importFn: () => Promise<any>): void {
    let timeoutId: NodeJS.Timeout;

    const handleMouseEnter = () => {
      timeoutId = setTimeout(() => {
        this.preloadChunk(chunkName, importFn);
      }, 100); // Small delay to avoid unnecessary preloads
    };

    const handleMouseLeave = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);

    // Cleanup function
    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }

  /**
   * Preload component when it enters viewport
   */
  preloadOnViewport(
    element: HTMLElement, 
    chunkName: string, 
    importFn: () => Promise<any>,
    options: IntersectionObserverInit = {}
  ): void {
    if (!('IntersectionObserver' in window)) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            this.preloadChunk(chunkName, importFn);
            observer.unobserve(element);
          }
        });
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
        ...options,
      }
    );

    observer.observe(element);

    // Cleanup function
    return () => {
      observer.unobserve(element);
    };
  }

  /**
   * Preload component when browser is idle
   */
  preloadOnIdle(chunkName: string, importFn: () => Promise<any>): void {
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => {
        this.preloadChunk(chunkName, importFn);
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        this.preloadChunk(chunkName, importFn);
      }, 1000);
    }
  }

  /**
   * Get preload status
   */
  getPreloadStatus(): {
    preloaded: string[];
    loading: string[];
    total: number;
  } {
    return {
      preloaded: Array.from(this.preloadedChunks),
      loading: Array.from(this.loadingChunks),
      total: this.preloadedChunks.size + this.loadingChunks.size,
    };
  }

  /**
   * Measure bundle performance
   */
  measureBundlePerformance(): void {
    if (typeof window === 'undefined') return;

    // Measure initial bundle load time
    const navigationTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigationTiming) {
      this.metrics.loadTime = navigationTiming.loadEventEnd - navigationTiming.fetchStart;
    }

    // Measure resource sizes (if available)
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    const jsResources = resources.filter(resource => 
      resource.name.includes('.js') && !resource.name.includes('node_modules')
    );

    this.metrics.totalChunks = jsResources.length;
    this.metrics.lazyChunks = jsResources
      .filter(resource => resource.name.includes('chunk'))
      .map(resource => resource.name);

    console.log('Bundle Performance Metrics:', this.metrics);
  }

  /**
   * Get bundle metrics
   */
  getBundleMetrics(): Partial<BundleMetrics> {
    return { ...this.metrics };
  }

  /**
   * Optimize bundle loading strategy
   */
  optimizeLoadingStrategy(strategy: PreloadStrategy): void {
    const { onHover, onViewport, onIdle, priority } = strategy;

    // Define component priorities
    const componentPriorities = {
      high: ['portfolio', 'contact'],
      medium: ['about'],
      low: ['video-modal', 'technical-skills'],
    };

    const componentsToOptimize = componentPriorities[priority] || [];

    // Note: Specific component preloading is handled by the LazyComponents module
    // This method sets the strategy but actual preloading happens in components
    console.log(`Bundle optimization strategy set: ${priority} priority for`, componentsToOptimize);
  }

  /**
   * Clear preload cache
   */
  clearPreloadCache(): void {
    this.preloadedChunks.clear();
    this.loadingChunks.clear();
  }

  /**
   * Get bundle size recommendations
   */
  getBundleRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.metrics.totalChunks && this.metrics.totalChunks > 10) {
      recommendations.push('Consider further code splitting for large chunks');
    }

    if (this.preloadedChunks.size === 0) {
      recommendations.push('Enable component preloading for better user experience');
    }

    if (this.metrics.loadTime && this.metrics.loadTime > 3000) {
      recommendations.push('Initial bundle load time is high, consider reducing main bundle size');
    }

    return recommendations;
  }

  /**
   * Enable development mode bundle analysis
   */
  enableBundleAnalysis(): void {
    if (process.env.NODE_ENV !== 'development') return;

    // Log bundle information
    console.group('Bundle Optimization Analysis');
    console.log('Preload Status:', this.getPreloadStatus());
    console.log('Bundle Metrics:', this.getBundleMetrics());
    console.log('Recommendations:', this.getBundleRecommendations());
    console.groupEnd();

    // Monitor chunk loading
    const originalImport = window.import || (() => {});
    (window as any).import = (...args: any[]) => {
      console.log('Dynamic import:', args[0]);
      return originalImport.apply(window, args);
    };
  }
}

// Create singleton instance
export const bundleOptimizationService = new BundleOptimizationService();

// React hook for bundle optimization
export function useBundleOptimization() {
  return {
    preloadChunk: bundleOptimizationService.preloadChunk.bind(bundleOptimizationService),
    preloadOnHover: bundleOptimizationService.preloadOnHover.bind(bundleOptimizationService),
    preloadOnViewport: bundleOptimizationService.preloadOnViewport.bind(bundleOptimizationService),
    preloadOnIdle: bundleOptimizationService.preloadOnIdle.bind(bundleOptimizationService),
    getPreloadStatus: bundleOptimizationService.getPreloadStatus.bind(bundleOptimizationService),
    getBundleMetrics: bundleOptimizationService.getBundleMetrics.bind(bundleOptimizationService),
  };
}

export default bundleOptimizationService;
