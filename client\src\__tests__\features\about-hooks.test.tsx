/**
 * About Feature Hooks Tests
 * Tests for about-related custom hooks
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useAbout, useAboutAnimations } from '@/features/about/hooks';

// Mock dependencies
vi.mock('@/hooks/useGSAP', () => ({
  useGSAP: () => ({
    gsap: {
      timeline: vi.fn(() => ({
        set: vi.fn().mockReturnThis(),
        to: vi.fn().mockReturnThis(),
        fromTo: vi.fn().mockReturnThis(),
        kill: vi.fn(),
      })),
      set: vi.fn(),
      to: vi.fn(),
      fromTo: vi.fn(),
    },
    isAvailable: true,
  }),
}));

vi.mock('@/utils', () => ({
  getAccessibilityConfig: () => ({
    reduceMotion: false,
    highContrast: false,
  }),
}));

describe('useAbout Hook', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useAbout());

    expect(result.current.personalInfo).toBeDefined();
    expect(result.current.personalInfo.name).toBe('Jaime Han');
    expect(result.current.skills).toHaveLength(5);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.activeSection).toBeNull();
    expect(result.current.animationsEnabled).toBe(true);
  });

  it('should update personal info', () => {
    const { result } = renderHook(() => useAbout());

    const newInfo = {
      ...result.current.personalInfo,
      name: 'Updated Name',
    };

    act(() => {
      result.current.setPersonalInfo(newInfo);
    });

    expect(result.current.personalInfo.name).toBe('Updated Name');
  });

  it('should update skills', () => {
    const { result } = renderHook(() => useAbout());

    const newSkills = [
      {
        id: '1',
        name: 'New Skill',
        category: 'frontend' as const,
        level: 'expert' as const,
        experience: '5+ years',
      },
    ];

    act(() => {
      result.current.setSkills(newSkills);
    });

    expect(result.current.skills).toHaveLength(1);
    expect(result.current.skills[0].name).toBe('New Skill');
  });

  it('should add a skill', () => {
    const { result } = renderHook(() => useAbout());

    const initialCount = result.current.skills.length;
    const newSkill = {
      id: '6',
      name: 'Added Skill',
      category: 'backend' as const,
      level: 'intermediate' as const,
      experience: '2 years',
    };

    act(() => {
      result.current.addSkill(newSkill);
    });

    expect(result.current.skills).toHaveLength(initialCount + 1);
    expect(result.current.skills.find(s => s.id === '6')).toEqual(newSkill);
  });

  it('should update a skill', () => {
    const { result } = renderHook(() => useAbout());

    act(() => {
      result.current.updateSkill('1', { level: 'beginner' });
    });

    const updatedSkill = result.current.skills.find(s => s.id === '1');
    expect(updatedSkill?.level).toBe('beginner');
  });

  it('should remove a skill', () => {
    const { result } = renderHook(() => useAbout());

    const initialCount = result.current.skills.length;

    act(() => {
      result.current.removeSkill('1');
    });

    expect(result.current.skills).toHaveLength(initialCount - 1);
    expect(result.current.skills.find(s => s.id === '1')).toBeUndefined();
  });

  it('should toggle animations', () => {
    const { result } = renderHook(() => useAbout());

    expect(result.current.animationsEnabled).toBe(true);

    act(() => {
      result.current.toggleAnimations();
    });

    expect(result.current.animationsEnabled).toBe(false);

    act(() => {
      result.current.toggleAnimations();
    });

    expect(result.current.animationsEnabled).toBe(true);
  });

  it('should set loading state', () => {
    const { result } = renderHook(() => useAbout());

    act(() => {
      result.current.setLoading(true);
    });

    expect(result.current.isLoading).toBe(true);
  });

  it('should set error state', () => {
    const { result } = renderHook(() => useAbout());

    act(() => {
      result.current.setError('Test error');
    });

    expect(result.current.error).toBe('Test error');
  });

  it('should set active section', () => {
    const { result } = renderHook(() => useAbout());

    act(() => {
      result.current.setActiveSection('skills');
    });

    expect(result.current.activeSection).toBe('skills');
  });
});

describe('useAboutAnimations Hook', () => {
  let mockRef: React.RefObject<HTMLElement>;

  beforeEach(() => {
    mockRef = {
      current: document.createElement('div'),
    };
  });

  it('should initialize with animation functions', () => {
    const { result } = renderHook(() => useAboutAnimations());

    expect(typeof result.current.animateAboutSection).toBe('function');
    expect(typeof result.current.animateSkillBars).toBe('function');
    expect(typeof result.current.animateScannerEffect).toBe('function');
    expect(typeof result.current.animateAIBrain).toBe('function');
    expect(typeof result.current.animatePersonalInfo).toBe('function');
    expect(typeof result.current.createScrollTriggerAnimation).toBe('function');
    expect(typeof result.current.cleanup).toBe('function');
    expect(result.current.isAvailable).toBe(true);
  });

  it('should animate about section', () => {
    const { result } = renderHook(() => useAboutAnimations());

    // Add some mock elements
    const element = document.createElement('div');
    element.setAttribute('data-animate-about', '');
    mockRef.current?.appendChild(element);

    act(() => {
      result.current.animateAboutSection(mockRef);
    });

    // Should not throw
    expect(result.current.animateAboutSection).toBeDefined();
  });

  it('should animate skill bars', () => {
    const { result } = renderHook(() => useAboutAnimations());

    // Add mock skill elements
    const skillItem = document.createElement('div');
    skillItem.setAttribute('data-skill-item', '');
    const progressBar = document.createElement('div');
    progressBar.setAttribute('data-skill-progress', '');
    progressBar.setAttribute('data-skill-level', '75%');
    
    mockRef.current?.appendChild(skillItem);
    mockRef.current?.appendChild(progressBar);

    act(() => {
      result.current.animateSkillBars(mockRef);
    });

    // Should not throw
    expect(result.current.animateSkillBars).toBeDefined();
  });

  it('should animate scanner effect', () => {
    const { result } = renderHook(() => useAboutAnimations());

    const mockCallback = vi.fn();

    act(() => {
      result.current.animateScannerEffect(mockRef, {
        isActive: true,
        scanLines: 3,
        speed: 2,
        color: '#00ff00',
        onScanComplete: mockCallback,
      });
    });

    // Should not throw
    expect(result.current.animateScannerEffect).toBeDefined();
  });

  it('should animate AI brain', () => {
    const { result } = renderHook(() => useAboutAnimations());

    act(() => {
      result.current.animateAIBrain(mockRef, true);
    });

    act(() => {
      result.current.animateAIBrain(mockRef, false);
    });

    // Should not throw
    expect(result.current.animateAIBrain).toBeDefined();
  });

  it('should animate personal info', () => {
    const { result } = renderHook(() => useAboutAnimations());

    // Add mock elements
    const avatar = document.createElement('div');
    avatar.setAttribute('data-avatar', '');
    const textElement = document.createElement('div');
    textElement.setAttribute('data-text-animate', '');
    
    mockRef.current?.appendChild(avatar);
    mockRef.current?.appendChild(textElement);

    act(() => {
      result.current.animatePersonalInfo(mockRef);
    });

    // Should not throw
    expect(result.current.animatePersonalInfo).toBeDefined();
  });

  it('should create scroll trigger animation', () => {
    const { result } = renderHook(() => useAboutAnimations());

    act(() => {
      result.current.createScrollTriggerAnimation(mockRef, 'fade');
    });

    act(() => {
      result.current.createScrollTriggerAnimation(mockRef, 'slide');
    });

    act(() => {
      result.current.createScrollTriggerAnimation(mockRef, 'scale');
    });

    act(() => {
      result.current.createScrollTriggerAnimation(mockRef, 'rotate');
    });

    // Should not throw
    expect(result.current.createScrollTriggerAnimation).toBeDefined();
  });

  it('should cleanup animations', () => {
    const { result } = renderHook(() => useAboutAnimations());

    act(() => {
      result.current.cleanup();
    });

    // Should not throw
    expect(result.current.cleanup).toBeDefined();
  });
});
