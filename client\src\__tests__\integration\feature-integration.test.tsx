/**
 * Feature Integration Tests
 * Tests for cross-feature interactions and integration
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { renderHook, act } from '@testing-library/react';

// Import feature modules
import { usePortfolio } from '@/features/portfolio/hooks';
import { useContactForm } from '@/features/contact/hooks';
import { useAbout } from '@/features/about/hooks';

// Import services
import { validationService } from '@/services/validationService';
import { animationService } from '@/services/animationService';

// Mock external dependencies
vi.mock('@/hooks/useGSAP', () => ({
  useGSAP: () => ({
    gsap: {
      timeline: vi.fn(() => ({
        set: vi.fn().mockReturnThis(),
        to: vi.fn().mockReturnThis(),
        fromTo: vi.fn().mockReturnThis(),
        kill: vi.fn(),
      })),
      set: vi.fn(),
      to: vi.fn(),
      fromTo: vi.fn(),
    },
    isAvailable: true,
  }),
}));

vi.mock('@/utils', () => ({
  getAccessibilityConfig: () => ({
    reduceMotion: false,
    highContrast: false,
  }),
}));

vi.mock('@/constants', () => ({
  PROJECTS: [
    {
      id: '1',
      title: 'Portfolio Website',
      description: 'Personal portfolio built with React',
      type: 'web',
      technologies: ['React', 'TypeScript', 'Tailwind'],
      status: 'completed',
      featured: true,
    },
  ],
}));

describe('Feature Integration Tests', () => {
  describe('Portfolio and Contact Integration', () => {
    it('should allow contacting about a specific project', () => {
      const { result: portfolioResult } = renderHook(() => usePortfolio());
      const { result: contactResult } = renderHook(() => useContactForm());

      // Select a project from portfolio
      act(() => {
        portfolioResult.current.selectProject(portfolioResult.current.projects[0]);
      });

      // Pre-fill contact form with project information
      act(() => {
        const project = portfolioResult.current.selectedProject;
        if (project) {
          contactResult.current.updateField('subject', `Inquiry about ${project.title}`);
          contactResult.current.updateField('message', `I'm interested in learning more about your ${project.title} project.`);
        }
      });

      expect(contactResult.current.data.subject).toBe('Inquiry about Portfolio Website');
      expect(contactResult.current.data.message).toContain('Portfolio Website');
    });

    it('should validate contact form with project-specific requirements', () => {
      const projectInquirySchema = {
        ...validationService.schemas.contact,
        subject: [
          validationService.createRules.required('Subject is required for project inquiries'),
          validationService.createRules.minLength(10, 'Subject must be descriptive'),
        ],
      };

      const validData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Inquiry about Portfolio Website project',
        message: 'I would like to know more about the technologies used in this project.',
      };

      const result = validationService.validate(validData, projectInquirySchema);
      expect(result.isValid).toBe(true);
    });
  });

  describe('About and Portfolio Integration', () => {
    it('should show skills used in portfolio projects', () => {
      const { result: aboutResult } = renderHook(() => useAbout());
      const { result: portfolioResult } = renderHook(() => usePortfolio());

      const portfolioTechnologies = portfolioResult.current.projects
        .flatMap(project => project.technologies);
      
      const relevantSkills = aboutResult.current.skills
        .filter(skill => portfolioTechnologies.includes(skill.name));

      expect(relevantSkills.length).toBeGreaterThan(0);
      expect(relevantSkills.some(skill => skill.name === 'React')).toBe(true);
    });

    it('should calculate experience based on portfolio projects', () => {
      const { result: aboutResult } = renderHook(() => useAbout());
      const { result: portfolioResult } = renderHook(() => usePortfolio());

      // Mock function to calculate experience from projects
      const calculateExperienceFromProjects = (skills: any[], projects: any[]) => {
        return skills.map(skill => {
          const projectsUsingSkill = projects.filter(project =>
            project.technologies.includes(skill.name)
          );
          
          return {
            ...skill,
            projectCount: projectsUsingSkill.length,
            hasPortfolioEvidence: projectsUsingSkill.length > 0,
          };
        });
      };

      const enhancedSkills = calculateExperienceFromProjects(
        aboutResult.current.skills,
        portfolioResult.current.projects
      );

      const reactSkill = enhancedSkills.find(skill => skill.name === 'React');
      expect(reactSkill?.hasPortfolioEvidence).toBe(true);
      expect(reactSkill?.projectCount).toBeGreaterThan(0);
    });
  });

  describe('Animation Service Integration', () => {
    it('should respect accessibility settings across features', () => {
      // Mock reduced motion preference
      vi.mocked(require('@/utils')).getAccessibilityConfig.mockReturnValue({
        reduceMotion: true,
        highContrast: false,
      });

      const mockGsap = {
        set: vi.fn(),
        to: vi.fn(),
        fromTo: vi.fn(),
      };

      // Test animation service respects reduced motion
      const fadeAnimation = animationService.createFadeIn(mockGsap, '.test-element');
      expect(fadeAnimation).toBeNull(); // Should not create animation

      // Test that elements are still made visible
      expect(mockGsap.set).toHaveBeenCalledWith('.test-element', { opacity: 1 });
    });

    it('should provide consistent animation patterns across features', () => {
      const mockGsap = {
        fromTo: vi.fn(),
        to: vi.fn(),
        set: vi.fn(),
      };

      // Test that different features use consistent animation patterns
      const portfolioAnimation = animationService.presets.fadeInUp(mockGsap, '.portfolio-item');
      const contactAnimation = animationService.presets.fadeInUp(mockGsap, '.contact-form');
      const aboutAnimation = animationService.presets.fadeInUp(mockGsap, '.about-section');

      // All should use the same underlying animation function
      expect(mockGsap.fromTo).toHaveBeenCalledTimes(3);
    });
  });

  describe('Validation Service Integration', () => {
    it('should provide consistent validation across features', () => {
      // Test email validation consistency
      const contactEmailRule = validationService.schemas.contact.email[1];
      const userEmailRule = validationService.schemas.user.email[1];

      const testEmail = 'invalid-email';
      
      expect(validationService.validateField(testEmail, contactEmailRule, 'email'))
        .toBe(validationService.validateField(testEmail, userEmailRule, 'email'));
    });

    it('should sanitize data consistently across features', () => {
      const maliciousData = {
        name: '<script>alert("xss")</script>John',
        message: '<b>Bold</b> message',
        description: 'Project <i>description</i>',
      };

      const sanitized = validationService.sanitize(maliciousData);

      expect(sanitized.name).toBe('John');
      expect(sanitized.message).toBe('Bold message');
      expect(sanitized.description).toBe('Project description');
    });
  });

  describe('State Management Integration', () => {
    it('should maintain state consistency across feature interactions', async () => {
      const { result: portfolioResult } = renderHook(() => usePortfolio());
      const { result: contactResult } = renderHook(() => useContactForm());

      // Simulate user workflow: browse portfolio -> contact about project
      act(() => {
        portfolioResult.current.selectProject(portfolioResult.current.projects[0]);
      });

      const selectedProject = portfolioResult.current.selectedProject;
      expect(selectedProject).toBeDefined();

      // Contact form should be able to reference the selected project
      act(() => {
        contactResult.current.updateField('subject', `About ${selectedProject?.title}`);
      });

      expect(contactResult.current.data.subject).toBe('About Portfolio Website');
    });

    it('should handle error states consistently', () => {
      const { result: portfolioResult } = renderHook(() => usePortfolio());
      const { result: contactResult } = renderHook(() => useContactForm());
      const { result: aboutResult } = renderHook(() => useAbout());

      // Set error states
      act(() => {
        portfolioResult.current.setError('Portfolio loading failed');
        contactResult.current.setError('email', 'Invalid email');
        aboutResult.current.setError('Skills loading failed');
      });

      // All should have error states
      expect(portfolioResult.current.error).toBe('Portfolio loading failed');
      expect(contactResult.current.errors.email).toBe('Invalid email');
      expect(aboutResult.current.error).toBe('Skills loading failed');

      // Clear errors
      act(() => {
        portfolioResult.current.setError(null);
        contactResult.current.clearError('email');
        aboutResult.current.setError(null);
      });

      // All errors should be cleared
      expect(portfolioResult.current.error).toBeNull();
      expect(contactResult.current.errors.email).toBeUndefined();
      expect(aboutResult.current.error).toBeNull();
    });
  });

  describe('Performance Integration', () => {
    it('should not cause memory leaks when switching between features', () => {
      const { result: portfolioResult } = renderHook(() => usePortfolio());
      const { result: contactResult } = renderHook(() => useContactForm());

      // Simulate rapid feature switching
      for (let i = 0; i < 10; i++) {
        act(() => {
          portfolioResult.current.selectProject(portfolioResult.current.projects[0]);
          portfolioResult.current.selectProject(null);
          
          contactResult.current.updateField('name', `Test ${i}`);
          contactResult.current.resetForm();
        });
      }

      // Should not throw or cause issues
      expect(portfolioResult.current.selectedProject).toBeNull();
      expect(contactResult.current.data.name).toBe('');
    });
  });
});
