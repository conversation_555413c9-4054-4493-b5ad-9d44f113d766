/**
 * Personal information section component
 */

import React from 'react';

interface PersonalInfoProps {
  className?: string;
}

export default function PersonalInfo({ className = '' }: PersonalInfoProps) {
  return (
    <div className={`space-y-8 ${className}`}>
      <div>
        <h3
          data-animate
          className="text-2xl md:text-3xl font-bold text-[#FF3366] mb-4 tracking-wide"
        >
          The Human Behind the Code
        </h3>
        <p
          data-animate
          className="text-gray-300 leading-relaxed text-lg"
        >
          I'm a passionate AI innovator who believes that the best digital experiences
          emerge from the intersection of cutting-edge technology and human creativity. With a
          background spanning from AI/ML to immersive web experiences, I craft solutions that
          don't just work but inspire.
        </p>
        <p
          data-animate
          className="text-gray-300 leading-relaxed text-lg mt-4"
        >
          I started out as a Business Analyst, but got tired of writing requirements 
          for other people's ideas, I wanted to build my own. I've earned 9 Azure certs,
          including AI Engineering, Data Scientist, and Solution Architect, 
          so I could go deep on both the tech and the strategy. 
          I'm not here to play it safe, I'm here to push what's possible.
        </p>
      </div>

      <div>
        <h4
          data-animate
          className="text-xl font-semibold text-[#4A90E2] mb-4"
        >
          What Drives Me
        </h4>
        <ul className="space-y-3">
          <li
            data-animate
            className="text-gray-300 flex items-start"
          >
            <span className="text-[#FF3366] mr-3 text-xl">◆</span>
            Building AI-powered applications that enhance human potential
          </li>
          <li
            data-animate
            className="text-gray-300 flex items-start"
          >
            <span className="text-[#4A90E2] mr-3 text-xl">◇</span>
            Creating immersive, interactive user experiences with GSAP and modern web tech
          </li>
          <li
            data-animate
            className="text-gray-300 flex items-start"
          >
            <span className="text-[#FF3366] mr-3 text-xl">◈</span>
            Pushing the edge of what's possible with coding agents and creative tech
          </li>
        </ul>
      </div>

      <div>
        <h4
          data-animate
          className="text-xl font-semibold text-[#4A90E2] mb-4"
        >
          Let's Build Something Amazing
        </h4>
        <p
          data-animate
          className="text-gray-300 leading-relaxed"
        >
          I'm always excited to collaborate on projects that push boundaries and create meaningful
          impact. Whether it's an AI-powered platform, an immersive web experience, or something
          completely unprecedented, let's explore what's possible together!
        </p>
      </div>
    </div>
  );
}
