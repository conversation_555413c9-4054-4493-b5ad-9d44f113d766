/**
 * About Feature Hook
 * Centralized state management and logic for about section functionality
 */

import { useState, useCallback, useMemo } from 'react';
import { Skill, PersonalInfo, AboutHookReturn, SkillFilter } from '../types';
import { aboutUtils } from '../utils/aboutUtils';

// Mock data - in a real app, this would come from an API or CMS
const mockPersonalInfo: PersonalInfo = {
  name: '<PERSON>',
  title: 'AI Engineer & Full-Stack Developer',
  location: 'San Francisco, CA',
  email: '<EMAIL>',
  bio: 'Passionate AI engineer with expertise in machine learning, full-stack development, and creating innovative digital experiences.',
  socialLinks: {
    github: 'https://github.com/jaimehan',
    linkedin: 'https://linkedin.com/in/jaimehan',
    website: 'https://jaimehan.dev',
  },
  interests: ['AI/ML', 'Web Development', 'Space Technology', 'Digital Art'],
  languages: ['English', 'Spanish', 'Python', 'JavaScript'],
};

const mockSkills: Skill[] = [
  {
    id: '1',
    name: 'React',
    category: 'frontend',
    level: 'expert',
    experience: '5+ years',
    description: 'Advanced React development with hooks, context, and performance optimization',
  },
  {
    id: '2',
    name: 'TypeScript',
    category: 'frontend',
    level: 'advanced',
    experience: '3+ years',
    description: 'Type-safe development with advanced TypeScript patterns',
  },
  {
    id: '3',
    name: 'Python',
    category: 'ai-ml',
    level: 'expert',
    experience: '6+ years',
    description: 'Machine learning, data science, and backend development',
  },
  {
    id: '4',
    name: 'TensorFlow',
    category: 'ai-ml',
    level: 'advanced',
    experience: '3+ years',
    description: 'Deep learning model development and deployment',
  },
  {
    id: '5',
    name: 'Node.js',
    category: 'backend',
    level: 'advanced',
    experience: '4+ years',
    description: 'Server-side JavaScript and API development',
  },
];

export const useAbout = (): AboutHookReturn => {
  const [personalInfo, setPersonalInfo] = useState<PersonalInfo>(mockPersonalInfo);
  const [skills, setSkills] = useState<Skill[]>(mockSkills);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [animationsEnabled, setAnimationsEnabled] = useState(true);

  const toggleAnimations = useCallback(() => {
    setAnimationsEnabled(prev => !prev);
  }, []);

  const addSkill = useCallback((skill: Skill) => {
    setSkills(prev => [...prev, skill]);
  }, []);

  const updateSkill = useCallback((id: string, updates: Partial<Skill>) => {
    setSkills(prev => prev.map(skill => 
      skill.id === id ? { ...skill, ...updates } : skill
    ));
  }, []);

  const removeSkill = useCallback((id: string) => {
    setSkills(prev => prev.filter(skill => skill.id !== id));
  }, []);

  // Computed values
  const skillsByCategory = useMemo(() => {
    return aboutUtils.groupSkillsByCategory(skills);
  }, [skills]);

  const skillLevelCounts = useMemo(() => {
    return aboutUtils.getSkillLevelCounts(skills);
  }, [skills]);

  return {
    personalInfo,
    skills,
    isLoading,
    error,
    activeSection,
    animationsEnabled,
    setPersonalInfo,
    setSkills,
    setLoading,
    setError,
    setActiveSection,
    toggleAnimations,
    addSkill,
    updateSkill,
    removeSkill,
  };
};

export default useAbout;
