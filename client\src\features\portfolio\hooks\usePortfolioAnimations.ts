/**
 * Portfolio Animations Hook
 * Specialized GSAP animations for portfolio components
 */

import { useRef, useCallback } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { getAccessibilityConfig } from '@/utils';
import { PortfolioAnimationConfig, PortfolioScrollTrigger } from '../types';

export const usePortfolioAnimations = () => {
  const { gsap, isAvailable } = useGSAP();
  const timelineRef = useRef<any>(null);
  const { reduceMotion } = getAccessibilityConfig();

  const defaultConfig: PortfolioAnimationConfig = {
    stagger: 0.1,
    duration: 0.8,
    ease: 'power2.out',
    reduceMotion,
  };

  const animatePortfolioGrid = useCallback((
    gridRef: React.RefObject<HTMLElement>,
    config: Partial<PortfolioAnimationConfig> = {}
  ) => {
    if (!isAvailable || !gridRef.current || reduceMotion) return;

    const finalConfig = { ...defaultConfig, ...config };
    const cards = gridRef.current.querySelectorAll('[data-portfolio-card]');

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline();

    timelineRef.current
      .set(cards, {
        opacity: 0,
        y: 50,
        scale: 0.9,
      })
      .to(cards, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: finalConfig.duration,
        ease: finalConfig.ease,
        stagger: finalConfig.stagger,
      });

    return timelineRef.current;
  }, [gsap, isAvailable, reduceMotion]);

  const animateProjectCard = useCallback((
    cardRef: React.RefObject<HTMLElement>,
    direction: 'enter' | 'leave' = 'enter'
  ) => {
    if (!isAvailable || !cardRef.current || reduceMotion) return;

    const isEnter = direction === 'enter';

    return gsap.to(cardRef.current, {
      scale: isEnter ? 1.05 : 1,
      y: isEnter ? -10 : 0,
      boxShadow: isEnter 
        ? '0 20px 40px rgba(0, 0, 0, 0.3)' 
        : '0 10px 20px rgba(0, 0, 0, 0.1)',
      duration: 0.3,
      ease: 'power2.out',
    });
  }, [gsap, isAvailable, reduceMotion]);

  const animateVideoModal = useCallback((
    modalRef: React.RefObject<HTMLElement>,
    isOpen: boolean
  ) => {
    if (!isAvailable || !modalRef.current || reduceMotion) {
      if (modalRef.current) {
        modalRef.current.style.opacity = isOpen ? '1' : '0';
        modalRef.current.style.transform = isOpen ? 'scale(1)' : 'scale(0.9)';
      }
      return;
    }

    if (isOpen) {
      gsap.set(modalRef.current, { opacity: 0, scale: 0.9 });
      return gsap.to(modalRef.current, {
        opacity: 1,
        scale: 1,
        duration: 0.3,
        ease: 'power2.out',
      });
    } else {
      return gsap.to(modalRef.current, {
        opacity: 0,
        scale: 0.9,
        duration: 0.2,
        ease: 'power2.in',
      });
    }
  }, [gsap, isAvailable, reduceMotion]);

  const createScrollTrigger = useCallback((
    element: React.RefObject<HTMLElement>,
    config: PortfolioScrollTrigger
  ) => {
    if (!isAvailable || !element.current || reduceMotion) return;

    return gsap.fromTo(element.current, 
      {
        opacity: 0,
        y: 100,
      },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: element.current,
          start: config.start || 'top 80%',
          end: config.end || 'bottom 20%',
          scrub: config.scrub || false,
          pin: config.pin || false,
        },
      }
    );
  }, [gsap, isAvailable, reduceMotion]);

  const cleanup = useCallback(() => {
    if (timelineRef.current) {
      timelineRef.current.kill();
      timelineRef.current = null;
    }
  }, []);

  return {
    animatePortfolioGrid,
    animateProjectCard,
    animateVideoModal,
    createScrollTrigger,
    cleanup,
    isAvailable,
  };
};

export default usePortfolioAnimations;
