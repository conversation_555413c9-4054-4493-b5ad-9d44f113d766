/**
 * Tests for Portfolio Section animations
 * Verifies that GSAP animations are properly initialized without window.gsap dependency
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import PortfolioSection from '@/components/PortfolioSection';

// Mock GSAP animations
const mockGSAP = {
  fromTo: vi.fn().mockReturnValue({ kill: vi.fn() }),
  to: vi.fn().mockReturnValue({ kill: vi.fn() }),
  set: vi.fn().mockReturnValue({ kill: vi.fn() }),
  registerPlugin: vi.fn(),
  killTweensOf: vi.fn(),
};

// Mock the useGSAP hook
vi.mock('@/hooks/useGSAP', () => ({
  useGSAP: () => ({
    gsap: mockGSAP,
    animate: vi.fn(),
    isAvailable: true,
    kill: vi.fn(),
  }),
  useScrollTrigger: () => ({
    createScrollTrigger: vi.fn(),
    isAvailable: true,
  }),
  useSpaceshipAnimation: () => ({
    createFlightAnimation: vi.fn(),
    createHoverEffect: vi.fn(() => ({ enter: vi.fn(), leave: vi.fn() })),
    createFloatingAnimation: vi.fn(),
    createLaserAnimation: vi.fn(),
    createSpaceshipSequence: vi.fn(),
    isAvailable: true,
  }),
}));

// Mock error boundary hook
vi.mock('@/components/ErrorBoundary', () => ({
  useErrorHandler: () => ({
    handleError: vi.fn(),
  }),
}));

// Mock utils
vi.mock('@/utils', () => ({
  getAccessibilityConfig: () => ({
    reduceMotion: false,
    highContrast: false,
  }),
}));

// Mock HTMLMediaElement methods for video player
Object.defineProperty(HTMLMediaElement.prototype, 'pause', {
  value: vi.fn(),
  writable: true,
});

Object.defineProperty(HTMLMediaElement.prototype, 'play', {
  value: vi.fn().mockResolvedValue(undefined),
  writable: true,
});

Object.defineProperty(HTMLMediaElement.prototype, 'load', {
  value: vi.fn(),
  writable: true,
});

describe('PortfolioSection Animations', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders portfolio section without errors', () => {
    render(<PortfolioSection />);

    expect(screen.getByText('MY WORK')).toBeInTheDocument();
    expect(screen.getByText(/A showcase of projects that blend cutting-edge AI technology/)).toBeInTheDocument();
  });

  it('initializes GSAP animations when available', () => {
    render(<PortfolioSection />);

    // Should render the portfolio section with GSAP available
    expect(screen.getByText('MY WORK')).toBeInTheDocument();
    // GSAP animations are handled by the hooks, which are mocked
  });

  it('renders all project cards', () => {
    render(<PortfolioSection />);

    // Check for project titles from PROJECTS constant (only 2 projects currently)
    expect(screen.getByText('Animated Portfolio Experience')).toBeInTheDocument();
    expect(screen.getByText('VLM Mobile App')).toBeInTheDocument();
  });

  it('handles reduced motion preference', () => {
    // Since the utils are already mocked at the top level, just render and test
    render(<PortfolioSection />);

    // Should still render content regardless of motion preference
    expect(screen.getByText('MY WORK')).toBeInTheDocument();
    expect(screen.getByText(/A showcase of projects that blend cutting-edge AI technology/)).toBeInTheDocument();
  });

  it('includes proper coming soon badges', () => {
    render(<PortfolioSection />);

    const comingSoonBadges = screen.getAllByText('Coming Soon');
    expect(comingSoonBadges).toHaveLength(2); // One for each project (2 projects currently)
  });

  it('includes project type labels', () => {
    render(<PortfolioSection />);

    expect(screen.getByText('Creative Dev Build')).toBeInTheDocument();
    expect(screen.getByText('VLM Experimentation')).toBeInTheDocument();
  });
}); 