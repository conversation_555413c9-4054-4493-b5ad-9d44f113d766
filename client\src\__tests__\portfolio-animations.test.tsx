/**
 * Tests for Portfolio Section animations
 * Verifies that GSAP animations are properly initialized without window.gsap dependency
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import PortfolioSection from '@/components/PortfolioSection';

// Mock GSAP animations
const mockGSAP = {
  fromTo: vi.fn().mockReturnValue({ kill: vi.fn() }),
  to: vi.fn().mockReturnValue({ kill: vi.fn() }),
  set: vi.fn().mockReturnValue({ kill: vi.fn() }),
  registerPlugin: vi.fn(),
  killTweensOf: vi.fn(),
};

// Mock the useGSAP hook
vi.mock('@/hooks/useGSAP', () => ({
  useGSAP: () => ({
    gsap: mockGSAP,
    animate: vi.fn(),
    isAvailable: true,
    kill: vi.fn(),
  }),
  useScrollTrigger: () => ({
    createScrollTrigger: vi.fn(),
    isAvailable: true,
  }),
  useSpaceshipAnimation: () => ({
    createFlightAnimation: vi.fn(),
    createHoverEffect: vi.fn(() => ({ enter: vi.fn(), leave: vi.fn() })),
    createFloatingAnimation: vi.fn(),
    createLaserAnimation: vi.fn(),
    createSpaceshipSequence: vi.fn(),
    isAvailable: true,
  }),
}));

// Mock error boundary hook
vi.mock('@/components/ErrorBoundary', () => ({
  useErrorHandler: () => ({
    handleError: vi.fn(),
  }),
}));

// Mock utils
vi.mock('@/utils', () => ({
  getAccessibilityConfig: () => ({
    reduceMotion: false,
    highContrast: false,
  }),
}));

describe('PortfolioSection Animations', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders portfolio section without errors', () => {
    render(<PortfolioSection />);
    
    expect(screen.getByText('FEATURED WORK')).toBeInTheDocument();
    expect(screen.getByText('Creative Solutions & Digital Experiences')).toBeInTheDocument();
  });

  it('initializes GSAP animations when available', () => {
    render(<PortfolioSection />);
    
    // Should call GSAP fromTo for title and cards
    expect(mockGSAP.fromTo).toHaveBeenCalled();
  });

  it('renders all project cards', () => {
    render(<PortfolioSection />);
    
    // Check for project titles from PROJECTS constant
    expect(screen.getByText('NeuralFlow AI Platform')).toBeInTheDocument();
    expect(screen.getByText('CineticUX Designer')).toBeInTheDocument();
    expect(screen.getByText('CloudSync Enterprise')).toBeInTheDocument();
    expect(screen.getByText('ReactFlow Studio')).toBeInTheDocument();
  });

  it('handles reduced motion preference', () => {
    // Mock reduced motion preference
    vi.mocked(require('@/utils').getAccessibilityConfig).mockReturnValue({
      reduceMotion: true,
      highContrast: false,
    });

    render(<PortfolioSection />);
    
    // Should use gsap.set instead of animated transitions
    expect(mockGSAP.set).toHaveBeenCalled();
  });

  it('includes proper coming soon badges', () => {
    render(<PortfolioSection />);
    
    const comingSoonBadges = screen.getAllByText('Coming Soon');
    expect(comingSoonBadges).toHaveLength(4); // One for each project
  });

  it('includes project type labels', () => {
    render(<PortfolioSection />);
    
    expect(screen.getByText('AI/ML Application')).toBeInTheDocument();
    expect(screen.getByText('Design Platform')).toBeInTheDocument();
    expect(screen.getByText('Enterprise Software')).toBeInTheDocument();
    expect(screen.getByText('Developer Tools')).toBeInTheDocument();
  });
}); 