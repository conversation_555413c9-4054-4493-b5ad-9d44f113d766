/**
 * Tests for ContactSection component
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ContactSection from '@/components/ContactSection';

// Mock GSAP and related hooks
vi.mock('@/hooks/useGSAP', () => ({
  useGSAP: () => ({
    gsap: {
      timeline: () => ({
        set: vi.fn().mockReturnThis(),
        to: vi.fn().mockReturnThis(),
        fromTo: vi.fn().mockReturnThis(),
        add: vi.fn().mockReturnThis(),
      }),
      set: vi.fn(),
    },
    animate: vi.fn(),
    isAvailable: true,
  }),
  useScrollTrigger: () => ({
    createScrollTrigger: vi.fn(),
    isAvailable: true,
  }),
  useSpaceshipAnimation: () => ({
    createFlightAnimation: vi.fn(),
    createHoverEffect: vi.fn(() => ({ enter: vi.fn(), leave: vi.fn() })),
    createFloatingAnimation: vi.fn(),
    createLaserAnimation: vi.fn(),
    createSpaceshipSequence: vi.fn(),
    isAvailable: true,
  }),
  useLetterAnimation: () => ({
    animateLetters: vi.fn(),
    animateLetterHover: vi.fn(),
    animateLetterClick: vi.fn(),
    resetLetterState: vi.fn(),
    createAnimationLoop: vi.fn(() => () => {}),
    stopAnimation: vi.fn(),
    isAvailable: true,
  }),
}));

vi.mock('@/components/ErrorBoundary', () => ({
  useErrorHandler: () => ({
    handleError: vi.fn(),
  }),
}));

vi.mock('@/utils', () => ({
  getAccessibilityConfig: () => ({
    reduceMotion: false,
  }),
}));

// Mock window.location.href
Object.defineProperty(window, 'location', {
  value: {
    href: '',
  },
  writable: true,
});

describe('ContactSection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the contact section with all elements', () => {
    render(<ContactSection />);
    
    // Check for main heading
    expect(screen.getByText('CONTACT ME')).toBeInTheDocument();
    
    // Check for invitation text
    expect(screen.getByText(/Ready to bring your ideas to life/)).toBeInTheDocument();
    
    // Check for email button
    expect(screen.getByText('Email Me')).toBeInTheDocument();
    
    // Check for form fields
    expect(screen.getByLabelText('Name')).toBeInTheDocument();
    expect(screen.getByLabelText(/Email/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Message/)).toBeInTheDocument();
  });

  it('handles form input changes', () => {
    render(<ContactSection />);
    
    const nameInput = screen.getByLabelText('Name') as HTMLInputElement;
    const emailInput = screen.getByLabelText(/Email/) as HTMLInputElement;
    const messageInput = screen.getByLabelText(/Message/) as HTMLTextAreaElement;
    
    fireEvent.change(nameInput, { target: { value: 'John Doe' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(messageInput, { target: { value: 'Hello there!' } });
    
    expect(nameInput.value).toBe('John Doe');
    expect(emailInput.value).toBe('<EMAIL>');
    expect(messageInput.value).toBe('Hello there!');
  });

  it('has proper form validation attributes', () => {
    render(<ContactSection />);
    
    const emailInput = screen.getByLabelText(/Email/);
    const messageInput = screen.getByLabelText(/Message/);
    
    expect(emailInput).toHaveAttribute('type', 'email');
    expect(emailInput).toHaveAttribute('required');
    expect(messageInput).toHaveAttribute('required');
  });

  it('has accessible form labels and structure', () => {
    render(<ContactSection />);

    // Check for proper section structure
    const section = screen.getByRole('region');
    expect(section).toBeInTheDocument();
    expect(section).toHaveAttribute('aria-labelledby', 'contact-heading');

    // Check for proper heading
    const heading = screen.getByRole('heading', { level: 2 });
    expect(heading).toHaveTextContent('CONTACT ME');
    expect(heading).toHaveAttribute('id', 'contact-heading');

    // Check for proper form structure
    const form = document.querySelector('form');
    expect(form).toBeInTheDocument();
  });

  it('email button triggers email functionality', () => {
    render(<ContactSection />);
    
    const emailButton = screen.getByRole('button', { name: /send me an email with your contact information/i });
    expect(emailButton).toBeInTheDocument();
    expect(emailButton).not.toBeDisabled();
    
    // Fill out form data
    const nameInput = screen.getByLabelText('Name') as HTMLInputElement;
    const emailInput = screen.getByLabelText(/Email/) as HTMLInputElement;
    const messageInput = screen.getByLabelText(/Message/) as HTMLTextAreaElement;
    
    fireEvent.change(nameInput, { target: { value: 'John Doe' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(messageInput, { target: { value: 'Test message' } });
    
    // Click the email button
    fireEvent.click(emailButton);
    
    // Check that window.location.href was set to mailto
    expect(window.location.href).toContain('mailto:<EMAIL>');
  });

  it('email button includes form data in mailto link', () => {
    render(<ContactSection />);
    
    const nameInput = screen.getByLabelText('Name') as HTMLInputElement;
    const emailInput = screen.getByLabelText(/Email/) as HTMLInputElement;
    const messageInput = screen.getByLabelText(/Message/) as HTMLTextAreaElement;
    const emailButton = screen.getByRole('button', { name: /send me an email with your contact information/i });
    
    // Fill out form with test data
    fireEvent.change(nameInput, { target: { value: 'Jane Smith' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(messageInput, { target: { value: 'This is a test message' } });
    
    // Click the email button
    fireEvent.click(emailButton);
    
    // Check that the mailto includes the form data (URL encoded)
    expect(window.location.href).toContain('Jane%20Smith');
    expect(window.location.href).toContain('jane%40example.com');
    expect(window.location.href).toContain('This%20is%20a%20test%20message');
  });

  it('has proper responsive classes', () => {
    render(<ContactSection />);
    
    const heading = screen.getByText('CONTACT ME');
    expect(heading).toHaveClass('text-4xl', 'md:text-5xl', 'lg:text-6xl');
  });

  it('includes spaceship elements', () => {
    const { container } = render(<ContactSection />);

    // Check for SVG spaceship components
    const svgElements = container.querySelectorAll('svg');
    const hasSpaceships = svgElements.length >= 2; // Should have at least 2 SVGs for spaceships

    expect(hasSpaceships).toBe(true);
  });
});
