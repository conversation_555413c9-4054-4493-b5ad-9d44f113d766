/**
 * Portfolio Utils Tests
 * Tests for portfolio utility functions
 */

import { describe, it, expect } from 'vitest';
import { portfolioUtils } from '@/features/portfolio/utils/portfolioUtils';
import type { Project } from '@/features/portfolio/types';

const mockProjects: Project[] = [
  {
    id: '1',
    title: 'React Portfolio',
    description: 'A portfolio website built with React',
    type: 'web',
    technologies: ['React', 'TypeScript', 'Tailwind'],
    status: 'completed',
    featured: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  },
  {
    id: '2',
    title: 'Mobile App',
    description: 'A mobile application',
    type: 'mobile',
    technologies: ['React Native', 'JavaScript'],
    status: 'in-progress',
    featured: false,
    createdAt: new Date('2023-02-01'),
    updatedAt: new Date('2023-02-01'),
  },
  {
    id: '3',
    title: 'AI Model',
    description: 'Machine learning model',
    type: 'ai-ml',
    technologies: ['Python', 'TensorFlow'],
    status: 'coming-soon',
    featured: true,
    createdAt: new Date('2023-03-01'),
    updatedAt: new Date('2023-03-01'),
  },
];

describe('portfolioUtils', () => {
  describe('transformProjectData', () => {
    it('should transform raw project data to Project interface', () => {
      const rawProject = {
        title: 'Test Project',
        description: 'Test description',
        type: 'web',
        technologies: ['React'],
      };

      const result = portfolioUtils.transformProjectData(rawProject);

      expect(result).toMatchObject({
        title: 'Test Project',
        description: 'Test description',
        type: 'web',
        technologies: ['React'],
        status: 'coming-soon',
        featured: false,
      });
      expect(result.id).toBeDefined();
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });

    it('should use provided values when available', () => {
      const rawProject = {
        id: 'custom-id',
        title: 'Test Project',
        description: 'Test description',
        type: 'web',
        status: 'completed',
        featured: true,
        createdAt: new Date('2023-01-01'),
      };

      const result = portfolioUtils.transformProjectData(rawProject);

      expect(result.id).toBe('custom-id');
      expect(result.status).toBe('completed');
      expect(result.featured).toBe(true);
      expect(result.createdAt).toEqual(new Date('2023-01-01'));
    });
  });

  describe('filterProjects', () => {
    it('should filter projects by type', () => {
      const filters = { type: ['web'], technology: [], status: [] };
      const result = portfolioUtils.filterProjects(mockProjects, filters);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('web');
    });

    it('should filter projects by technology', () => {
      const filters = { type: [], technology: ['React'], status: [] };
      const result = portfolioUtils.filterProjects(mockProjects, filters);

      expect(result).toHaveLength(2);
      expect(result.every(p => p.technologies.some(t => t.includes('React')))).toBe(true);
    });

    it('should filter projects by status', () => {
      const filters = { type: [], technology: [], status: ['completed'] };
      const result = portfolioUtils.filterProjects(mockProjects, filters);

      expect(result).toHaveLength(1);
      expect(result[0].status).toBe('completed');
    });

    it('should apply multiple filters', () => {
      const filters = { type: ['web'], technology: ['React'], status: ['completed'] };
      const result = portfolioUtils.filterProjects(mockProjects, filters);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('web');
      expect(result[0].technologies).toContain('React');
      expect(result[0].status).toBe('completed');
    });

    it('should return all projects when no filters applied', () => {
      const filters = { type: [], technology: [], status: [] };
      const result = portfolioUtils.filterProjects(mockProjects, filters);

      expect(result).toHaveLength(3);
    });
  });

  describe('sortProjects', () => {
    it('should sort projects by title ascending', () => {
      const sortOption = { key: 'title' as keyof Project, direction: 'asc' as const };
      const result = portfolioUtils.sortProjects(mockProjects, sortOption);

      expect(result[0].title).toBe('AI Model');
      expect(result[1].title).toBe('Mobile App');
      expect(result[2].title).toBe('React Portfolio');
    });

    it('should sort projects by title descending', () => {
      const sortOption = { key: 'title' as keyof Project, direction: 'desc' as const };
      const result = portfolioUtils.sortProjects(mockProjects, sortOption);

      expect(result[0].title).toBe('React Portfolio');
      expect(result[1].title).toBe('Mobile App');
      expect(result[2].title).toBe('AI Model');
    });

    it('should sort projects by creation date', () => {
      const sortOption = { key: 'createdAt' as keyof Project, direction: 'asc' as const };
      const result = portfolioUtils.sortProjects(mockProjects, sortOption);

      expect(result[0].createdAt).toEqual(new Date('2023-01-01'));
      expect(result[1].createdAt).toEqual(new Date('2023-02-01'));
      expect(result[2].createdAt).toEqual(new Date('2023-03-01'));
    });
  });

  describe('getFilterOptions', () => {
    it('should return unique filter options', () => {
      const result = portfolioUtils.getFilterOptions(mockProjects);

      expect(result.types).toEqual(['web', 'mobile', 'ai-ml']);
      expect(result.technologies).toEqual(['React', 'TypeScript', 'Tailwind', 'React Native', 'JavaScript', 'Python', 'TensorFlow']);
      expect(result.statuses).toEqual(['completed', 'in-progress', 'coming-soon']);
    });
  });

  describe('searchProjects', () => {
    it('should search projects by title', () => {
      const result = portfolioUtils.searchProjects(mockProjects, 'React');

      expect(result).toHaveLength(2); // "React Portfolio" and "Mobile App" (React Native)
      expect(result.some(p => p.title === 'React Portfolio')).toBe(true);
    });

    it('should search projects by description', () => {
      const result = portfolioUtils.searchProjects(mockProjects, 'machine learning');

      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('AI Model');
    });

    it('should search projects by technology', () => {
      const result = portfolioUtils.searchProjects(mockProjects, 'TypeScript');

      expect(result).toHaveLength(1);
      expect(result[0].technologies).toContain('TypeScript');
    });

    it('should return all projects for empty query', () => {
      const result = portfolioUtils.searchProjects(mockProjects, '');

      expect(result).toHaveLength(3);
    });

    it('should be case insensitive', () => {
      const result = portfolioUtils.searchProjects(mockProjects, 'REACT');

      expect(result).toHaveLength(2); // "React Portfolio" and "Mobile App" (React Native)
      expect(result.some(p => p.title === 'React Portfolio')).toBe(true);
    });
  });

  describe('getFeaturedProjects', () => {
    it('should return only featured projects', () => {
      const result = portfolioUtils.getFeaturedProjects(mockProjects);

      expect(result).toHaveLength(2);
      expect(result.every(p => p.featured)).toBe(true);
    });
  });

  describe('getProjectsByStatus', () => {
    it('should return projects with specific status', () => {
      const result = portfolioUtils.getProjectsByStatus(mockProjects, 'completed');

      expect(result).toHaveLength(1);
      expect(result[0].status).toBe('completed');
    });
  });

  describe('validateProject', () => {
    it('should validate complete project', () => {
      const project = {
        title: 'Valid Project',
        description: 'Valid description',
        type: 'web',
        technologies: ['React'],
      };

      const result = portfolioUtils.validateProject(project);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for invalid project', () => {
      const project = {
        title: '',
        description: '',
        type: '',
        technologies: [],
      };

      const result = portfolioUtils.validateProject(project);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Title is required');
      expect(result.errors).toContain('Description is required');
      expect(result.errors).toContain('Type is required');
      expect(result.errors).toContain('At least one technology is required');
    });
  });

  describe('generateProjectSlug', () => {
    it('should generate valid slug from title', () => {
      const result = portfolioUtils.generateProjectSlug('My Awesome Project!');

      expect(result).toBe('my-awesome-project');
    });

    it('should handle special characters', () => {
      const result = portfolioUtils.generateProjectSlug('React & TypeScript App (2023)');

      expect(result).toBe('react-typescript-app-2023');
    });
  });

  describe('getProjectProgress', () => {
    it('should return correct progress for different statuses', () => {
      expect(portfolioUtils.getProjectProgress({ status: 'completed' } as Project)).toBe(100);
      expect(portfolioUtils.getProjectProgress({ status: 'in-progress' } as Project)).toBe(50);
      expect(portfolioUtils.getProjectProgress({ status: 'coming-soon' } as Project)).toBe(0);
    });
  });
});
