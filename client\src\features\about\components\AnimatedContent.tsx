/**
 * Animated content reveal component with staggered animations
 */

import React, { useRef, useEffect, useCallback } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { ANIMATION_DURATIONS, ANIMATION_EASINGS } from '@/constants';

interface AnimatedContentProps {
  children: React.ReactNode;
  autoStart?: boolean;
  startDelay?: number;
  staggerDelay?: number;
  onComplete?: () => void;
  className?: string;
  animationType?: 'fadeUp' | 'slideLeft' | 'slideRight' | 'scale';
}

export default function AnimatedContent({
  children,
  autoStart = false,
  startDelay = 0,
  staggerDelay = 0.12,
  onComplete,
  className = '',
  animationType = 'fadeUp'
}: AnimatedContentProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRefs = useRef<(HTMLElement | null)[]>([]);
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  // Animation configurations
  const animationConfigs = {
    fadeUp: {
      from: { opacity: 0, y: 30, filter: 'blur(2px)' },
      to: { opacity: 1, y: 0, filter: 'blur(0px)' }
    },
    slideLeft: {
      from: { opacity: 0, x: -50, filter: 'blur(2px)' },
      to: { opacity: 1, x: 0, filter: 'blur(0px)' }
    },
    slideRight: {
      from: { opacity: 0, x: 50, filter: 'blur(2px)' },
      to: { opacity: 1, x: 0, filter: 'blur(0px)' }
    },
    scale: {
      from: { opacity: 0, scale: 0.8, filter: 'blur(2px)' },
      to: { opacity: 1, scale: 1, filter: 'blur(0px)' }
    }
  };

  const config = animationConfigs[animationType];

  // Hide content initially
  const hideContentInitially = useCallback(() => {
    if (!gsap || !isAvailable) return;

    try {
      contentRefs.current.forEach((element) => {
        if (element) {
          gsap.set(element, {
            ...config.from,
            visibility: 'hidden'
          });
        }
      });
    } catch (error) {
      handleError(error as Error);
    }
  }, [gsap, isAvailable, config.from, handleError]);

  // Start animation
  const startAnimation = useCallback(() => {
    try {
      if (!gsap || !isAvailable) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) {
        // Set final states for reduced motion
        contentRefs.current.forEach((element) => {
          if (element) {
            gsap.set(element, {
              opacity: 1,
              y: 0,
              x: 0,
              scale: 1,
              filter: 'blur(0px)',
              visibility: 'visible'
            });
          }
        });
        onComplete?.();
        return;
      }

      const timeline = gsap.timeline({
        onComplete
      });

      // Animate each content element with stagger
      contentRefs.current.forEach((element, index) => {
        if (element) {
          const startTime = index * staggerDelay;
          
          // Set initial state (make visible for animation)
          timeline.set(element, {
            ...config.from,
            visibility: 'visible',
            willChange: 'transform'
          }, startTime);

          // Animate to final state
          timeline.to(element, {
            ...config.to,
            duration: ANIMATION_DURATIONS.SLOW,
            ease: ANIMATION_EASINGS.POWER2_OUT,
            onComplete: () => {
              // Remove will-change after animation
              if (element.style) {
                element.style.willChange = 'auto';
              }
            }
          }, startTime);
        }
      });

    } catch (error) {
      handleError(error as Error);
    }
  }, [gsap, isAvailable, config, staggerDelay, onComplete, handleError]);

  // Collect content elements
  const collectContentElements = useCallback(() => {
    if (!containerRef.current) return;

    const elements = containerRef.current.querySelectorAll('[data-animate]');
    contentRefs.current = Array.from(elements) as HTMLElement[];
  }, []);

  // Auto-start effect
  useEffect(() => {
    if (autoStart && isAvailable) {
      collectContentElements();
      hideContentInitially();
      
      const timer = setTimeout(startAnimation, startDelay);
      return () => clearTimeout(timer);
    }
  }, [autoStart, isAvailable, collectContentElements, hideContentInitially, startAnimation, startDelay]);

  // Initialize content as hidden when GSAP becomes available
  useEffect(() => {
    if (isAvailable) {
      collectContentElements();
      hideContentInitially();
    }
  }, [isAvailable, collectContentElements, hideContentInitially]);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
}

// Higher-order component for easier usage
export function withAnimatedContent<T extends object>(
  WrappedComponent: React.ComponentType<T>,
  animationProps: Partial<AnimatedContentProps> = {}
) {
  return function AnimatedWrapper(props: T) {
    return (
      <AnimatedContent {...animationProps}>
        <WrappedComponent {...props} />
      </AnimatedContent>
    );
  };
}

// Hook for manual animation control
export function useAnimatedContent(
  animationType: AnimatedContentProps['animationType'] = 'fadeUp'
) {
  const contentRefs = useRef<(HTMLElement | null)[]>([]);
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  const animateContent = useCallback((
    elements: HTMLElement[],
    options: {
      staggerDelay?: number;
      onComplete?: () => void;
    } = {}
  ) => {
    const { staggerDelay = 0.12, onComplete } = options;
    
    try {
      if (!gsap || !isAvailable) return;

      const config = {
        fadeUp: {
          from: { opacity: 0, y: 30, filter: 'blur(2px)' },
          to: { opacity: 1, y: 0, filter: 'blur(0px)' }
        },
        slideLeft: {
          from: { opacity: 0, x: -50, filter: 'blur(2px)' },
          to: { opacity: 1, x: 0, filter: 'blur(0px)' }
        },
        slideRight: {
          from: { opacity: 0, x: 50, filter: 'blur(2px)' },
          to: { opacity: 1, x: 0, filter: 'blur(0px)' }
        },
        scale: {
          from: { opacity: 0, scale: 0.8, filter: 'blur(2px)' },
          to: { opacity: 1, scale: 1, filter: 'blur(0px)' }
        }
      }[animationType];

      const timeline = gsap.timeline({ onComplete });

      elements.forEach((element, index) => {
        if (element) {
          const startTime = index * staggerDelay;
          
          timeline.set(element, {
            ...config.from,
            visibility: 'visible'
          }, startTime);

          timeline.to(element, {
            ...config.to,
            duration: ANIMATION_DURATIONS.SLOW,
            ease: ANIMATION_EASINGS.POWER2_OUT
          }, startTime);
        }
      });

    } catch (error) {
      handleError(error as Error);
    }
  }, [gsap, isAvailable, animationType, handleError]);

  return {
    animateContent,
    isAvailable
  };
}
