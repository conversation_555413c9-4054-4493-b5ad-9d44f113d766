/**
 * Predefined animation configurations and presets
 * Provides consistent animation patterns across the application
 */

import type { AnimationConfig, LetterAnimationConfig } from '@/types';
import { 
  ANIMATION_DURATIONS, 
  ANIMATION_EASINGS, 
  COLOR_VARIANTS 
} from '@/constants';

/**
 * Common entrance animations
 */
export const ENTRANCE_ANIMATIONS = {
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  slideInUp: {
    from: { opacity: 0, y: 50 },
    to: { opacity: 1, y: 0, duration: ANIMATION_DURATIONS.SLOW, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  slideInDown: {
    from: { opacity: 0, y: -50 },
    to: { opacity: 1, y: 0, duration: ANIMATION_DURATIONS.SLOW, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  slideInLeft: {
    from: { opacity: 0, x: -50 },
    to: { opacity: 1, x: 0, duration: ANIMATION_DURATIONS.SLOW, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  slideInRight: {
    from: { opacity: 0, x: 50 },
    to: { opacity: 1, x: 0, duration: ANIMATION_DURATIONS.SLOW, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  scaleIn: {
    from: { opacity: 0, scale: 0.8 },
    to: { opacity: 1, scale: 1, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.BACK_OUT }
  },
  
  rotateIn: {
    from: { opacity: 0, rotation: -180, scale: 0.8 },
    to: { opacity: 1, rotation: 0, scale: 1, duration: ANIMATION_DURATIONS.SLOW, ease: ANIMATION_EASINGS.BACK_OUT }
  }
} as const;

/**
 * Common exit animations
 */
export const EXIT_ANIMATIONS = {
  fadeOut: {
    to: { opacity: 0, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_IN }
  },
  
  slideOutUp: {
    to: { opacity: 0, y: -50, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.POWER2_IN }
  },
  
  slideOutDown: {
    to: { opacity: 0, y: 50, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.POWER2_IN }
  },
  
  scaleOut: {
    to: { opacity: 0, scale: 0.8, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_IN }
  }
} as const;

/**
 * Hover effect animations
 */
export const HOVER_ANIMATIONS = {
  lift: {
    enter: { y: -5, scale: 1.02, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_OUT },
    leave: { y: 0, scale: 1, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  glow: {
    enter: { 
      boxShadow: '0 0 20px rgba(255, 255, 255, 0.3)', 
      duration: ANIMATION_DURATIONS.FAST, 
      ease: ANIMATION_EASINGS.POWER2_OUT 
    },
    leave: { 
      boxShadow: '0 0 0px rgba(255, 255, 255, 0)', 
      duration: ANIMATION_DURATIONS.FAST, 
      ease: ANIMATION_EASINGS.POWER2_OUT 
    }
  },
  
  scale: {
    enter: { scale: 1.05, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_OUT },
    leave: { scale: 1, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  rotate: {
    enter: { rotation: 5, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_OUT },
    leave: { rotation: 0, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_OUT }
  }
} as const;

/**
 * Letter animation configurations
 */
export const LETTER_ANIMATIONS: Record<string, LetterAnimationConfig> = {
  glow: {
    type: 'glow',
    duration: ANIMATION_DURATIONS.NORMAL,
    ease: ANIMATION_EASINGS.POWER2_OUT,
    color: COLOR_VARIANTS.BLUE,
  },
  
  rotation: {
    type: 'rotation',
    duration: ANIMATION_DURATIONS.SLOW,
    ease: ANIMATION_EASINGS.POWER2_IN_OUT,
    rotation: 360,
  },
  
  bounce: {
    type: 'bounce',
    duration: ANIMATION_DURATIONS.NORMAL,
    ease: ANIMATION_EASINGS.BOUNCE_OUT,
    scale: 1.2,
  },
  
  flip: {
    type: 'flip',
    duration: ANIMATION_DURATIONS.SLOW,
    ease: ANIMATION_EASINGS.POWER2_IN_OUT,
    rotation: 180,
  },
  
  pulse: {
    type: 'pulse',
    duration: ANIMATION_DURATIONS.FAST,
    ease: ANIMATION_EASINGS.POWER2_OUT,
    scale: 1.1,
    repeat: 1,
    yoyo: true,
  }
} as const;

/**
 * Scroll-triggered animation presets
 */
export const SCROLL_ANIMATIONS = {
  fadeInUp: {
    trigger: "top 80%",
    from: { opacity: 0, y: 80 },
    to: { opacity: 1, y: 0, duration: ANIMATION_DURATIONS.SLOW, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  staggeredCards: {
    trigger: "top 85%",
    from: { opacity: 0, y: 80, scale: 0.9 },
    to: { opacity: 1, y: 0, scale: 1, duration: ANIMATION_DURATIONS.SLOW, ease: ANIMATION_EASINGS.POWER2_OUT },
    stagger: 0.2
  },
  
  parallaxSlow: {
    trigger: "top bottom",
    end: "bottom top",
    scrub: 1,
    from: { y: 0 },
    to: { y: -50 }
  },
  
  parallaxFast: {
    trigger: "top bottom",
    end: "bottom top", 
    scrub: 0.5,
    from: { y: 0 },
    to: { y: -100 }
  }
} as const;

/**
 * Loading and transition animations
 */
export const TRANSITION_ANIMATIONS = {
  pageEnter: {
    from: { opacity: 0, scale: 0.95 },
    to: { opacity: 1, scale: 1, duration: ANIMATION_DURATIONS.SLOW, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  pageExit: {
    to: { opacity: 0, scale: 1.05, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.POWER2_IN }
  },
  
  modalEnter: {
    from: { opacity: 0, scale: 0.8, y: 50 },
    to: { opacity: 1, scale: 1, y: 0, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.BACK_OUT }
  },
  
  modalExit: {
    to: { opacity: 0, scale: 0.8, y: 50, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_IN }
  }
} as const;

/**
 * Continuous animation loops
 */
export const LOOP_ANIMATIONS = {
  float: {
    y: 10,
    duration: 2,
    ease: "power2.inOut",
    repeat: -1,
    yoyo: true
  },
  
  rotate: {
    rotation: 360,
    duration: 10,
    ease: "none",
    repeat: -1
  },
  
  pulse: {
    scale: 1.05,
    duration: 1.5,
    ease: "power2.inOut",
    repeat: -1,
    yoyo: true
  },
  
  glow: {
    boxShadow: "0 0 30px rgba(255, 255, 255, 0.5)",
    duration: 2,
    ease: "power2.inOut",
    repeat: -1,
    yoyo: true
  }
} as const;

/**
 * Performance-optimized animation presets
 */
export const PERFORMANCE_ANIMATIONS = {
  // Use transform properties for better performance
  optimizedSlide: {
    from: { opacity: 0, transform: 'translateY(50px)' },
    to: { opacity: 1, transform: 'translateY(0px)', duration: ANIMATION_DURATIONS.NORMAL }
  },
  
  optimizedScale: {
    from: { opacity: 0, transform: 'scale(0.8)' },
    to: { opacity: 1, transform: 'scale(1)', duration: ANIMATION_DURATIONS.NORMAL }
  },
  
  optimizedRotate: {
    from: { opacity: 0, transform: 'rotate(-10deg)' },
    to: { opacity: 1, transform: 'rotate(0deg)', duration: ANIMATION_DURATIONS.NORMAL }
  }
} as const;

/**
 * Utility function to get animation preset by name
 */
export function getAnimationPreset(
  category: 'entrance' | 'exit' | 'hover' | 'scroll' | 'transition' | 'loop' | 'performance',
  name: string
): any {
  const presets = {
    entrance: ENTRANCE_ANIMATIONS,
    exit: EXIT_ANIMATIONS,
    hover: HOVER_ANIMATIONS,
    scroll: SCROLL_ANIMATIONS,
    transition: TRANSITION_ANIMATIONS,
    loop: LOOP_ANIMATIONS,
    performance: PERFORMANCE_ANIMATIONS
  };
  
  return presets[category]?.[name as keyof typeof presets[typeof category]];
}

/**
 * Utility function to create custom animation config
 */
export function createAnimationConfig(
  basePreset: any,
  overrides: Partial<AnimationConfig> = {}
): AnimationConfig {
  return {
    ...basePreset,
    ...overrides
  };
}
