/**
 * Spaceship animation components and utilities
 */

import React, { useRef, useEffect } from 'react';
import { useSpaceshipAnimation } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import type { SpaceshipAnimationConfig } from './types';

interface SpaceshipBeamProps {
  beamRef: React.RefObject<SVGGElement>;
  isActive?: boolean;
  type?: 'trace' | 'pulse' | 'fadeOut';
  config?: SpaceshipAnimationConfig;
}

/**
 * Spaceship beam animation component
 */
export function SpaceshipBeam({ 
  beamRef, 
  isActive = false, 
  type = 'trace',
  config 
}: SpaceshipBeamProps) {
  const { createLaserAnimation } = useSpaceshipAnimation();
  const { handleError } = useErrorHandler();
  const animationRef = useRef<any>(null);

  useEffect(() => {
    if (!beamRef.current) return;

    try {
      if (isActive) {
        animationRef.current = createLaserAnimation(beamRef.current, type);
      } else if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }
    } catch (error) {
      handleError(error as Error);
    }

    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
    };
  }, [isActive, type, createLaserAnimation, beamRef, handleError]);

  return null; // This is a utility component, no render
}

interface SpaceshipGlowProps {
  glowRef: React.RefObject<SVGGElement>;
  isActive?: boolean;
  intensity?: number;
  color?: string;
}

/**
 * Spaceship glow effect component
 */
export function SpaceshipGlow({ 
  glowRef, 
  isActive = false, 
  intensity = 1,
  color = '#4A90E2'
}: SpaceshipGlowProps) {
  const animationRef = useRef<any>(null);
  const { handleError } = useErrorHandler();

  useEffect(() => {
    if (!glowRef.current) return;

    try {
      const gsap = require('gsap').gsap;

      if (isActive) {
        animationRef.current = gsap.to(glowRef.current, {
          opacity: 0.8 * intensity,
          scale: 1.2 * intensity,
          filter: `drop-shadow(0 0 ${20 * intensity}px ${color})`,
          duration: 0.3,
          ease: 'power2.out'
        });
      } else {
        animationRef.current = gsap.to(glowRef.current, {
          opacity: 0.3,
          scale: 1,
          filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))',
          duration: 0.3,
          ease: 'power2.out'
        });
      }
    } catch (error) {
      handleError(error as Error);
    }

    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
    };
  }, [isActive, intensity, color, glowRef, handleError]);

  return null;
}

interface SpaceshipLightsProps {
  lightsRef: React.RefObject<SVGGElement>;
  isActive?: boolean;
  pattern?: 'blink' | 'pulse' | 'rotate';
}

/**
 * Spaceship lights animation component
 */
export function SpaceshipLights({ 
  lightsRef, 
  isActive = false, 
  pattern = 'blink' 
}: SpaceshipLightsProps) {
  const animationRef = useRef<any>(null);
  const { handleError } = useErrorHandler();

  useEffect(() => {
    if (!lightsRef.current) return;

    try {
      const gsap = require('gsap').gsap;

      if (isActive) {
        const lights = lightsRef.current.querySelectorAll('circle');

        switch (pattern) {
          case 'blink':
            animationRef.current = gsap.to(lights, {
              opacity: 0.3,
              duration: 0.5,
              repeat: -1,
              yoyo: true,
              stagger: 0.1,
              ease: 'power2.inOut'
            });
            break;

          case 'pulse':
            animationRef.current = gsap.to(lights, {
              scale: 1.5,
              opacity: 0.8,
              duration: 1,
              repeat: -1,
              yoyo: true,
              stagger: 0.2,
              ease: 'power2.inOut'
            });
            break;

          case 'rotate':
            animationRef.current = gsap.to(lightsRef.current, {
              rotation: 360,
              duration: 3,
              repeat: -1,
              ease: 'none'
            });
            break;
        }
      } else if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }
    } catch (error) {
      handleError(error as Error);
    }

    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
    };
  }, [isActive, pattern, lightsRef, handleError]);

  return null;
}

interface SpaceshipExhaustProps {
  exhaustRef: React.RefObject<SVGGElement>;
  isActive?: boolean;
  intensity?: number;
}

/**
 * Spaceship exhaust animation component
 */
export function SpaceshipExhaust({ 
  exhaustRef, 
  isActive = false, 
  intensity = 1 
}: SpaceshipExhaustProps) {
  const animationRef = useRef<any>(null);
  const { handleError } = useErrorHandler();

  useEffect(() => {
    if (!exhaustRef.current) return;

    try {
      const gsap = require('gsap').gsap;

      if (isActive) {
        const exhaustElements = exhaustRef.current.querySelectorAll('*');

        animationRef.current = gsap.timeline({ repeat: -1 })
          .to(exhaustElements, {
            scaleY: 1.5 * intensity,
            opacity: 0.9,
            duration: 0.3,
            stagger: 0.05,
            ease: 'power2.out'
          })
          .to(exhaustElements, {
            scaleY: 0.8,
            opacity: 0.6,
            duration: 0.4,
            stagger: 0.05,
            ease: 'power2.inOut'
          });
      } else if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }
    } catch (error) {
      handleError(error as Error);
    }

    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
    };
  }, [isActive, intensity, exhaustRef, handleError]);

  return null;
}

interface AnimatedSpaceshipWrapperProps {
  children: React.ReactNode;
  enableBeam?: boolean;
  enableGlow?: boolean;
  enableLights?: boolean;
  enableExhaust?: boolean;
  beamRef?: React.RefObject<SVGGElement>;
  glowRef?: React.RefObject<SVGGElement>;
  lightsRef?: React.RefObject<SVGGElement>;
  exhaustRef?: React.RefObject<SVGGElement>;
  isActive?: boolean;
  config?: {
    beam?: { type?: 'trace' | 'pulse' | 'fadeOut' };
    glow?: { intensity?: number; color?: string };
    lights?: { pattern?: 'blink' | 'pulse' | 'rotate' };
    exhaust?: { intensity?: number };
  };
}

/**
 * Wrapper component that manages all spaceship animations
 */
export function AnimatedSpaceshipWrapper({
  children,
  enableBeam = false,
  enableGlow = false,
  enableLights = false,
  enableExhaust = false,
  beamRef,
  glowRef,
  lightsRef,
  exhaustRef,
  isActive = false,
  config = {}
}: AnimatedSpaceshipWrapperProps) {
  return (
    <>
      {children}
      
      {enableBeam && beamRef && (
        <SpaceshipBeam
          beamRef={beamRef}
          isActive={isActive}
          type={config.beam?.type}
        />
      )}
      
      {enableGlow && glowRef && (
        <SpaceshipGlow
          glowRef={glowRef}
          isActive={isActive}
          intensity={config.glow?.intensity}
          color={config.glow?.color}
        />
      )}
      
      {enableLights && lightsRef && (
        <SpaceshipLights
          lightsRef={lightsRef}
          isActive={isActive}
          pattern={config.lights?.pattern}
        />
      )}
      
      {enableExhaust && exhaustRef && (
        <SpaceshipExhaust
          exhaustRef={exhaustRef}
          isActive={isActive}
          intensity={config.exhaust?.intensity}
        />
      )}
    </>
  );
}
