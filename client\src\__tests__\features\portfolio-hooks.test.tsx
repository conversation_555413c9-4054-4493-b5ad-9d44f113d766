/**
 * Portfolio Feature Hooks Tests
 * Tests for portfolio-related custom hooks
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { usePortfolio, usePortfolioAnimations } from '@/features/portfolio/hooks';

// Mock GSAP and dependencies
vi.mock('@/hooks/useGSAP', () => ({
  useGSAP: () => ({
    gsap: {
      timeline: vi.fn(() => ({
        set: vi.fn().mockReturnThis(),
        to: vi.fn().mockReturnThis(),
        fromTo: vi.fn().mockReturnThis(),
        kill: vi.fn(),
      })),
      set: vi.fn(),
      to: vi.fn(),
      fromTo: vi.fn(),
    },
    isAvailable: true,
  }),
}));

vi.mock('@/utils', () => ({
  getAccessibilityConfig: () => ({
    reduceMotion: false,
    highContrast: false,
  }),
}));

vi.mock('@/constants', () => ({
  PROJECTS: [
    {
      id: '1',
      title: 'Test Project 1',
      description: 'Test description 1',
      type: 'web',
      technologies: ['React', 'TypeScript'],
      status: 'completed',
      featured: true,
    },
    {
      id: '2',
      title: 'Test Project 2',
      description: 'Test description 2',
      type: 'mobile',
      technologies: ['React Native'],
      status: 'in-progress',
      featured: false,
    },
  ],
}));

describe('usePortfolio Hook', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => usePortfolio());

    expect(result.current.projects).toHaveLength(2);
    expect(result.current.selectedProject).toBeNull();
    expect(result.current.isVideoModalOpen).toBe(false);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.filters).toEqual({
      type: [],
      technology: [],
      status: [],
    });
  });

  it('should select a project', () => {
    const { result } = renderHook(() => usePortfolio());

    act(() => {
      result.current.selectProject(result.current.projects[0]);
    });

    expect(result.current.selectedProject).toEqual(result.current.projects[0]);
  });

  it('should open video modal with project', () => {
    const { result } = renderHook(() => usePortfolio());

    act(() => {
      result.current.openVideoModal(result.current.projects[0]);
    });

    expect(result.current.selectedProject).toEqual(result.current.projects[0]);
    expect(result.current.isVideoModalOpen).toBe(true);
  });

  it('should close video modal', () => {
    const { result } = renderHook(() => usePortfolio());

    // First open modal
    act(() => {
      result.current.openVideoModal(result.current.projects[0]);
    });

    // Then close it
    act(() => {
      result.current.closeVideoModal();
    });

    expect(result.current.selectedProject).toBeNull();
    expect(result.current.isVideoModalOpen).toBe(false);
  });

  it('should update filters', () => {
    const { result } = renderHook(() => usePortfolio());

    act(() => {
      result.current.updateFilters({ type: ['web'] });
    });

    expect(result.current.filters.type).toEqual(['web']);
  });

  it('should clear filters', () => {
    const { result } = renderHook(() => usePortfolio());

    // First set some filters
    act(() => {
      result.current.updateFilters({ type: ['web'], status: ['completed'] });
    });

    // Then clear them
    act(() => {
      result.current.clearFilters();
    });

    expect(result.current.filters).toEqual({
      type: [],
      technology: [],
      status: [],
    });
  });

  it('should set loading state', () => {
    const { result } = renderHook(() => usePortfolio());

    act(() => {
      result.current.setLoading(true);
    });

    expect(result.current.loading).toBe(true);
  });

  it('should set error state', () => {
    const { result } = renderHook(() => usePortfolio());

    act(() => {
      result.current.setError('Test error');
    });

    expect(result.current.error).toBe('Test error');
  });
});

describe('usePortfolioAnimations Hook', () => {
  let mockRef: React.RefObject<HTMLElement>;

  beforeEach(() => {
    mockRef = {
      current: document.createElement('div'),
    };
  });

  it('should initialize with animation functions', () => {
    const { result } = renderHook(() => usePortfolioAnimations());

    expect(typeof result.current.animatePortfolioGrid).toBe('function');
    expect(typeof result.current.animateProjectCard).toBe('function');
    expect(typeof result.current.animateVideoModal).toBe('function');
    expect(typeof result.current.createScrollTrigger).toBe('function');
    expect(typeof result.current.cleanup).toBe('function');
    expect(result.current.isAvailable).toBe(true);
  });

  it('should animate portfolio grid', () => {
    const { result } = renderHook(() => usePortfolioAnimations());

    // Add some mock cards to the ref
    const card1 = document.createElement('div');
    card1.setAttribute('data-portfolio-card', '');
    const card2 = document.createElement('div');
    card2.setAttribute('data-portfolio-card', '');
    
    mockRef.current?.appendChild(card1);
    mockRef.current?.appendChild(card2);

    act(() => {
      result.current.animatePortfolioGrid(mockRef);
    });

    // Should not throw and should return a timeline-like object
    expect(result.current.animatePortfolioGrid).toBeDefined();
  });

  it('should animate project card on hover', () => {
    const { result } = renderHook(() => usePortfolioAnimations());

    act(() => {
      result.current.animateProjectCard(mockRef, 'enter');
    });

    act(() => {
      result.current.animateProjectCard(mockRef, 'leave');
    });

    // Should not throw
    expect(result.current.animateProjectCard).toBeDefined();
  });

  it('should animate video modal', () => {
    const { result } = renderHook(() => usePortfolioAnimations());

    act(() => {
      result.current.animateVideoModal(mockRef, true);
    });

    act(() => {
      result.current.animateVideoModal(mockRef, false);
    });

    // Should not throw
    expect(result.current.animateVideoModal).toBeDefined();
  });

  it('should create scroll trigger', () => {
    const { result } = renderHook(() => usePortfolioAnimations());

    act(() => {
      result.current.createScrollTrigger(mockRef, {
        trigger: mockRef.current!,
        start: 'top 80%',
        end: 'bottom 20%',
      });
    });

    // Should not throw
    expect(result.current.createScrollTrigger).toBeDefined();
  });

  it('should cleanup animations', () => {
    const { result } = renderHook(() => usePortfolioAnimations());

    act(() => {
      result.current.cleanup();
    });

    // Should not throw
    expect(result.current.cleanup).toBeDefined();
  });
});
