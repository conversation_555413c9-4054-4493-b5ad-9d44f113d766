/**
 * State Manager Service
 * Centralized state management utilities and patterns
 */

import { useState, useCallback, useEffect, useRef } from 'react';

export interface StateManagerConfig {
  persist?: boolean;
  storageKey?: string;
  debounceMs?: number;
  validateState?: (state: any) => boolean;
}

export interface StateSubscription<T> {
  id: string;
  callback: (state: T) => void;
  selector?: (state: T) => any;
}

/**
 * Global State Manager Class
 */
class GlobalStateManager<T extends Record<string, any>> {
  private state: T;
  private subscriptions: Map<string, StateSubscription<T>>;
  private config: StateManagerConfig;

  constructor(initialState: T, config: StateManagerConfig = {}) {
    this.state = initialState;
    this.subscriptions = new Map();
    this.config = {
      persist: false,
      debounceMs: 0,
      ...config,
    };

    // Load persisted state if enabled
    if (this.config.persist && this.config.storageKey) {
      this.loadPersistedState();
    }
  }

  /**
   * Get current state
   */
  getState(): T {
    return { ...this.state };
  }

  /**
   * Set state
   */
  setState(updates: Partial<T> | ((prevState: T) => Partial<T>)): void {
    const newUpdates = typeof updates === 'function' ? updates(this.state) : updates;
    
    // Validate state if validator provided
    const newState = { ...this.state, ...newUpdates };
    if (this.config.validateState && !this.config.validateState(newState)) {
      console.warn('State validation failed, update rejected');
      return;
    }

    this.state = newState;
    this.notifySubscribers();

    // Persist state if enabled
    if (this.config.persist && this.config.storageKey) {
      this.persistState();
    }
  }

  /**
   * Subscribe to state changes
   */
  subscribe(callback: (state: T) => void, selector?: (state: T) => any): string {
    const id = Math.random().toString(36).substr(2, 9);
    this.subscriptions.set(id, { id, callback, selector });
    return id;
  }

  /**
   * Unsubscribe from state changes
   */
  unsubscribe(id: string): void {
    this.subscriptions.delete(id);
  }

  /**
   * Notify all subscribers
   */
  private notifySubscribers(): void {
    this.subscriptions.forEach(({ callback, selector }) => {
      const selectedState = selector ? selector(this.state) : this.state;
      callback(selectedState);
    });
  }

  /**
   * Load persisted state from localStorage
   */
  private loadPersistedState(): void {
    try {
      const stored = localStorage.getItem(this.config.storageKey!);
      if (stored) {
        const parsedState = JSON.parse(stored);
        this.state = { ...this.state, ...parsedState };
      }
    } catch (error) {
      console.warn('Failed to load persisted state:', error);
    }
  }

  /**
   * Persist state to localStorage
   */
  private persistState(): void {
    try {
      localStorage.setItem(this.config.storageKey!, JSON.stringify(this.state));
    } catch (error) {
      console.warn('Failed to persist state:', error);
    }
  }

  /**
   * Reset state to initial values
   */
  reset(initialState?: T): void {
    this.state = initialState || {} as T;
    this.notifySubscribers();
    
    if (this.config.persist && this.config.storageKey) {
      localStorage.removeItem(this.config.storageKey);
    }
  }
}

/**
 * React hook for using global state
 */
export function useGlobalState<T, S = T>(
  stateManager: GlobalStateManager<T>,
  selector?: (state: T) => S
): [S, (updates: Partial<T> | ((prevState: T) => Partial<T>)) => void] {
  const [state, setState] = useState<S>(() => {
    const currentState = stateManager.getState();
    return selector ? selector(currentState) : (currentState as unknown as S);
  });

  const selectorRef = useRef(selector);
  selectorRef.current = selector;

  useEffect(() => {
    const subscriptionId = stateManager.subscribe((newState) => {
      const selectedState = selectorRef.current ? selectorRef.current(newState) : (newState as unknown as S);
      setState(selectedState);
    }, selector);

    return () => {
      stateManager.unsubscribe(subscriptionId);
    };
  }, [stateManager]);

  const updateState = useCallback((updates: Partial<T> | ((prevState: T) => Partial<T>)) => {
    stateManager.setState(updates);
  }, [stateManager]);

  return [state, updateState];
}

/**
 * Create a new state manager instance
 */
export function createStateManager<T extends Record<string, any>>(
  initialState: T,
  config?: StateManagerConfig
): GlobalStateManager<T> {
  return new GlobalStateManager(initialState, config);
}

/**
 * Local state hook with enhanced features
 */
export function useEnhancedState<T>(
  initialState: T,
  config: {
    persist?: boolean;
    storageKey?: string;
    debounceMs?: number;
    validator?: (state: T) => boolean;
  } = {}
): [T, (updates: Partial<T> | ((prev: T) => Partial<T>)) => void, { reset: () => void; isValid: boolean }] {
  const [state, setState] = useState<T>(() => {
    if (config.persist && config.storageKey) {
      try {
        const stored = localStorage.getItem(config.storageKey);
        if (stored) {
          return { ...initialState, ...JSON.parse(stored) };
        }
      } catch (error) {
        console.warn('Failed to load persisted state:', error);
      }
    }
    return initialState;
  });

  const debounceRef = useRef<NodeJS.Timeout>();
  const initialStateRef = useRef(initialState);

  const updateState = useCallback((updates: Partial<T> | ((prev: T) => Partial<T>)) => {
    setState(prevState => {
      const newUpdates = typeof updates === 'function' ? updates(prevState) : updates;
      const newState = { ...prevState, ...newUpdates };

      // Validate state if validator provided
      if (config.validator && !config.validator(newState)) {
        console.warn('State validation failed, update rejected');
        return prevState;
      }

      // Persist state if enabled
      if (config.persist && config.storageKey) {
        if (debounceRef.current) {
          clearTimeout(debounceRef.current);
        }

        const persistFn = () => {
          try {
            localStorage.setItem(config.storageKey!, JSON.stringify(newState));
          } catch (error) {
            console.warn('Failed to persist state:', error);
          }
        };

        if (config.debounceMs && config.debounceMs > 0) {
          debounceRef.current = setTimeout(persistFn, config.debounceMs);
        } else {
          persistFn();
        }
      }

      return newState;
    });
  }, [config]);

  const reset = useCallback(() => {
    setState(initialStateRef.current);
    if (config.persist && config.storageKey) {
      localStorage.removeItem(config.storageKey);
    }
  }, [config]);

  const isValid = config.validator ? config.validator(state) : true;

  return [state, updateState, { reset, isValid }];
}

/**
 * Application-wide state managers
 */

// UI State Manager
export interface UIState {
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
  loading: boolean;
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    timestamp: number;
  }>;
}

export const uiStateManager = createStateManager<UIState>({
  theme: 'dark',
  sidebarOpen: false,
  loading: false,
  notifications: [],
}, {
  persist: true,
  storageKey: 'portfolio-ui-state',
});

// User Preferences State Manager
export interface UserPreferences {
  animationsEnabled: boolean;
  soundEnabled: boolean;
  language: string;
  accessibility: {
    reduceMotion: boolean;
    highContrast: boolean;
    fontSize: 'small' | 'medium' | 'large';
  };
}

export const userPreferencesManager = createStateManager<UserPreferences>({
  animationsEnabled: true,
  soundEnabled: false,
  language: 'en',
  accessibility: {
    reduceMotion: false,
    highContrast: false,
    fontSize: 'medium',
  },
}, {
  persist: true,
  storageKey: 'portfolio-user-preferences',
  validateState: (state) => {
    return typeof state.animationsEnabled === 'boolean' &&
           typeof state.soundEnabled === 'boolean' &&
           typeof state.language === 'string';
  },
});

export default {
  createStateManager,
  useGlobalState,
  useEnhancedState,
  uiStateManager,
  userPreferencesManager,
};
