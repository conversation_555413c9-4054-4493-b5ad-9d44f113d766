/**
 * Portfolio Utility Functions
 * Helper functions for portfolio data manipulation and processing
 */

import { Project, ProjectFilter, SortOption } from '../types';

export const portfolioUtils = {
  /**
   * Transform raw project data to Project interface
   */
  transformProjectData: (rawProject: any): Project => {
    return {
      id: rawProject.id || crypto.randomUUID(),
      title: rawProject.title,
      description: rawProject.description,
      type: rawProject.type,
      technologies: rawProject.technologies || [],
      image: rawProject.image,
      video: rawProject.video,
      demoUrl: rawProject.demoUrl,
      githubUrl: rawProject.githubUrl,
      status: rawProject.status || 'coming-soon',
      featured: rawProject.featured || false,
      createdAt: rawProject.createdAt || new Date(),
      updatedAt: rawProject.updatedAt || new Date(),
    };
  },

  /**
   * Filter projects based on criteria
   */
  filterProjects: (
    projects: Project[], 
    filters: { type: string[]; technology: string[]; status: string[] }
  ): Project[] => {
    return projects.filter(project => {
      // Type filter
      if (filters.type.length > 0 && !filters.type.includes(project.type)) {
        return false;
      }

      // Technology filter
      if (filters.technology.length > 0) {
        const hasMatchingTech = filters.technology.some(tech =>
          project.technologies.some(projectTech =>
            projectTech.toLowerCase().includes(tech.toLowerCase())
          )
        );
        if (!hasMatchingTech) return false;
      }

      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(project.status)) {
        return false;
      }

      return true;
    });
  },

  /**
   * Sort projects by specified criteria
   */
  sortProjects: (projects: Project[], sortOption: SortOption): Project[] => {
    return [...projects].sort((a, b) => {
      const aValue = a[sortOption.key];
      const bValue = b[sortOption.key];

      if (sortOption.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  },

  /**
   * Get unique values for filter options
   */
  getFilterOptions: (projects: Project[]) => {
    const types = [...new Set(projects.map(p => p.type))];
    const technologies = [...new Set(projects.flatMap(p => p.technologies))];
    const statuses = [...new Set(projects.map(p => p.status))];

    return { types, technologies, statuses };
  },

  /**
   * Search projects by title or description
   */
  searchProjects: (projects: Project[], query: string): Project[] => {
    if (!query.trim()) return projects;

    const lowercaseQuery = query.toLowerCase();
    return projects.filter(project =>
      project.title.toLowerCase().includes(lowercaseQuery) ||
      project.description.toLowerCase().includes(lowercaseQuery) ||
      project.technologies.some(tech =>
        tech.toLowerCase().includes(lowercaseQuery)
      )
    );
  },

  /**
   * Get featured projects
   */
  getFeaturedProjects: (projects: Project[]): Project[] => {
    return projects.filter(project => project.featured);
  },

  /**
   * Get projects by status
   */
  getProjectsByStatus: (projects: Project[], status: Project['status']): Project[] => {
    return projects.filter(project => project.status === status);
  },

  /**
   * Format project for sharing
   */
  formatProjectForShare: (project: Project) => {
    return {
      title: project.title,
      description: project.description,
      url: project.demoUrl || project.githubUrl,
      technologies: project.technologies.join(', '),
    };
  },

  /**
   * Validate project data
   */
  validateProject: (project: Partial<Project>): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!project.title?.trim()) {
      errors.push('Title is required');
    }

    if (!project.description?.trim()) {
      errors.push('Description is required');
    }

    if (!project.type?.trim()) {
      errors.push('Type is required');
    }

    if (!project.technologies || project.technologies.length === 0) {
      errors.push('At least one technology is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Generate project slug for URLs
   */
  generateProjectSlug: (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  },

  /**
   * Calculate project completion percentage
   */
  getProjectProgress: (project: Project): number => {
    switch (project.status) {
      case 'completed':
        return 100;
      case 'in-progress':
        return 50; // Could be more sophisticated based on actual progress data
      case 'coming-soon':
        return 0;
      default:
        return 0;
    }
  },
};
