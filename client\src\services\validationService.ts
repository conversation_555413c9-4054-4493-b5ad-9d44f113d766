/**
 * Validation Service
 * Centralized validation utilities and rules
 */

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
  message?: string;
}

export interface ValidationSchema {
  [key: string]: ValidationRule | ValidationRule[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  firstError?: string;
}

export interface ValidationOptions {
  stopOnFirstError?: boolean;
  trimValues?: boolean;
  allowEmpty?: boolean;
}

class ValidationService {
  private commonPatterns = {
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    phone: /^[\+]?[1-9][\d]{0,15}$/,
    url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
    alphanumeric: /^[a-zA-Z0-9]+$/,
    alphabetic: /^[a-zA-Z]+$/,
    numeric: /^[0-9]+$/,
    strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  };

  /**
   * Validate a single value against a rule
   */
  validateField(value: any, rule: ValidationRule, fieldName?: string): string | null {
    // Handle empty values
    if (value === null || value === undefined || value === '') {
      if (rule.required) {
        return rule.message || `${fieldName || 'Field'} is required`;
      }
      return null; // Empty but not required is valid
    }

    // Convert to string for length and pattern checks
    const stringValue = String(value).trim();

    // Min length check
    if (rule.minLength !== undefined && stringValue.length < rule.minLength) {
      return rule.message || `${fieldName || 'Field'} must be at least ${rule.minLength} characters`;
    }

    // Max length check
    if (rule.maxLength !== undefined && stringValue.length > rule.maxLength) {
      return rule.message || `${fieldName || 'Field'} must be no more than ${rule.maxLength} characters`;
    }

    // Pattern check
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      return rule.message || `${fieldName || 'Field'} format is invalid`;
    }

    // Custom validation
    if (rule.custom) {
      const customResult = rule.custom(value);
      if (customResult) {
        return customResult;
      }
    }

    return null; // Valid
  }

  /**
   * Validate an object against a schema
   */
  validate(
    data: Record<string, any>,
    schema: ValidationSchema,
    options: ValidationOptions = {}
  ): ValidationResult {
    const {
      stopOnFirstError = false,
      trimValues = true,
      allowEmpty = false,
    } = options;

    const errors: Record<string, string> = {};
    let processedData = { ...data };

    // Trim values if requested
    if (trimValues) {
      Object.keys(processedData).forEach(key => {
        if (typeof processedData[key] === 'string') {
          processedData[key] = processedData[key].trim();
        }
      });
    }

    // Validate each field
    for (const [fieldName, rules] of Object.entries(schema)) {
      const value = processedData[fieldName];
      const fieldRules = Array.isArray(rules) ? rules : [rules];

      for (const rule of fieldRules) {
        const error = this.validateField(value, rule, fieldName);
        if (error) {
          errors[fieldName] = error;
          if (stopOnFirstError) {
            break;
          }
          break; // Only show first error per field
        }
      }

      if (stopOnFirstError && Object.keys(errors).length > 0) {
        break;
      }
    }

    const isValid = Object.keys(errors).length === 0;
    const firstError = Object.values(errors)[0];

    return {
      isValid,
      errors,
      firstError,
    };
  }

  /**
   * Create validation rules for common patterns
   */
  createRules = {
    required: (message?: string): ValidationRule => ({
      required: true,
      message,
    }),

    email: (message?: string): ValidationRule => ({
      pattern: this.commonPatterns.email,
      message: message || 'Please enter a valid email address',
    }),

    phone: (message?: string): ValidationRule => ({
      pattern: this.commonPatterns.phone,
      message: message || 'Please enter a valid phone number',
    }),

    url: (message?: string): ValidationRule => ({
      pattern: this.commonPatterns.url,
      message: message || 'Please enter a valid URL',
    }),

    minLength: (length: number, message?: string): ValidationRule => ({
      minLength: length,
      message,
    }),

    maxLength: (length: number, message?: string): ValidationRule => ({
      maxLength: length,
      message,
    }),

    length: (min: number, max: number, message?: string): ValidationRule => ({
      minLength: min,
      maxLength: max,
      message,
    }),

    strongPassword: (message?: string): ValidationRule => ({
      pattern: this.commonPatterns.strongPassword,
      message: message || 'Password must contain at least 8 characters, including uppercase, lowercase, number, and special character',
    }),

    custom: (validator: (value: any) => string | null): ValidationRule => ({
      custom: validator,
    }),

    oneOf: (allowedValues: any[], message?: string): ValidationRule => ({
      custom: (value) => {
        if (!allowedValues.includes(value)) {
          return message || `Value must be one of: ${allowedValues.join(', ')}`;
        }
        return null;
      },
    }),

    numeric: (message?: string): ValidationRule => ({
      pattern: this.commonPatterns.numeric,
      message: message || 'Please enter only numbers',
    }),

    alphabetic: (message?: string): ValidationRule => ({
      pattern: this.commonPatterns.alphabetic,
      message: message || 'Please enter only letters',
    }),

    alphanumeric: (message?: string): ValidationRule => ({
      pattern: this.commonPatterns.alphanumeric,
      message: message || 'Please enter only letters and numbers',
    }),
  };

  /**
   * Pre-built schemas for common forms
   */
  schemas = {
    contact: {
      name: [
        this.createRules.required('Name is required'),
        this.createRules.minLength(2, 'Name must be at least 2 characters'),
        this.createRules.maxLength(50, 'Name must be no more than 50 characters'),
      ],
      email: [
        this.createRules.required('Email is required'),
        this.createRules.email(),
      ],
      message: [
        this.createRules.required('Message is required'),
        this.createRules.minLength(10, 'Message must be at least 10 characters'),
        this.createRules.maxLength(1000, 'Message must be no more than 1000 characters'),
      ],
      phone: [
        this.createRules.phone(),
      ],
    },

    user: {
      username: [
        this.createRules.required('Username is required'),
        this.createRules.minLength(3, 'Username must be at least 3 characters'),
        this.createRules.maxLength(20, 'Username must be no more than 20 characters'),
        this.createRules.alphanumeric('Username can only contain letters and numbers'),
      ],
      email: [
        this.createRules.required('Email is required'),
        this.createRules.email(),
      ],
      password: [
        this.createRules.required('Password is required'),
        this.createRules.strongPassword(),
      ],
    },

    project: {
      title: [
        this.createRules.required('Project title is required'),
        this.createRules.minLength(3, 'Title must be at least 3 characters'),
        this.createRules.maxLength(100, 'Title must be no more than 100 characters'),
      ],
      description: [
        this.createRules.required('Description is required'),
        this.createRules.minLength(10, 'Description must be at least 10 characters'),
        this.createRules.maxLength(500, 'Description must be no more than 500 characters'),
      ],
      type: [
        this.createRules.required('Project type is required'),
        this.createRules.oneOf(['web', 'mobile', 'ai-ml', 'other'], 'Invalid project type'),
      ],
      technologies: [
        this.createRules.custom((value) => {
          if (!Array.isArray(value) || value.length === 0) {
            return 'At least one technology is required';
          }
          return null;
        }),
      ],
    },
  };

  /**
   * Sanitize input data
   */
  sanitize(data: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};

    Object.keys(data).forEach(key => {
      let value = data[key];

      if (typeof value === 'string') {
        // Trim whitespace
        value = value.trim();
        
        // Remove HTML tags
        value = value.replace(/<[^>]*>/g, '');
        
        // Escape special characters for basic XSS prevention
        value = value
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#x27;');
      }

      sanitized[key] = value;
    });

    return sanitized;
  }

  /**
   * Check if email domain exists (basic check)
   */
  async validateEmailDomain(email: string): Promise<boolean> {
    try {
      const domain = email.split('@')[1];
      if (!domain) return false;

      // Basic domain format validation
      const domainPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
      return domainPattern.test(domain);
    } catch {
      return false;
    }
  }

  /**
   * Validate file upload
   */
  validateFile(
    file: File,
    options: {
      maxSize?: number; // in bytes
      allowedTypes?: string[];
      allowedExtensions?: string[];
    } = {}
  ): ValidationResult {
    const errors: Record<string, string> = {};

    // Check file size
    if (options.maxSize && file.size > options.maxSize) {
      errors.size = `File size must be less than ${Math.round(options.maxSize / 1024 / 1024)}MB`;
    }

    // Check file type
    if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
      errors.type = `File type ${file.type} is not allowed`;
    }

    // Check file extension
    if (options.allowedExtensions) {
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (!extension || !options.allowedExtensions.includes(extension)) {
        errors.extension = `File extension .${extension} is not allowed`;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      firstError: Object.values(errors)[0],
    };
  }
}

// Create singleton instance
export const validationService = new ValidationService();

export default validationService;
