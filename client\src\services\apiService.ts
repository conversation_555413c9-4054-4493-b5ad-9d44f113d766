/**
 * API Service
 * Centralized API communication and data fetching service
 */

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
  status: number;
}

export interface ApiRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  cache?: boolean;
}

class ApiService {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private cache: Map<string, { data: any; timestamp: number; ttl: number }>;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
    this.cache = new Map();
  }

  /**
   * Make HTTP request
   */
  private async request<T>(
    endpoint: string,
    config: ApiRequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = 10000,
      retries = 3,
      cache = false,
    } = config;

    const url = `${this.baseUrl}${endpoint}`;
    const cacheKey = `${method}:${url}:${JSON.stringify(body)}`;

    // Check cache for GET requests
    if (method === 'GET' && cache) {
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return {
          data: cached,
          success: true,
          status: 200,
        };
      }
    }

    const requestConfig: RequestInit = {
      method,
      headers: { ...this.defaultHeaders, ...headers },
      signal: AbortSignal.timeout(timeout),
    };

    if (body && method !== 'GET') {
      requestConfig.body = JSON.stringify(body);
    }

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, requestConfig);
        const data = await response.json();

        if (response.ok) {
          // Cache successful GET requests
          if (method === 'GET' && cache) {
            this.setCache(cacheKey, data, 5 * 60 * 1000); // 5 minutes TTL
          }

          return {
            data,
            success: true,
            status: response.status,
          };
        } else {
          return {
            data: null,
            success: false,
            error: data.message || response.statusText,
            status: response.status,
          };
        }
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < retries) {
          // Exponential backoff
          await new Promise(resolve => 
            setTimeout(resolve, Math.pow(2, attempt) * 1000)
          );
        }
      }
    }

    return {
      data: null,
      success: false,
      error: lastError?.message || 'Request failed',
      status: 0,
    };
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, config?: Omit<ApiRequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, body?: any, config?: Omit<ApiRequestConfig, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'POST', body });
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, body?: any, config?: Omit<ApiRequestConfig, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PUT', body });
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string, config?: Omit<ApiRequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, body?: any, config?: Omit<ApiRequestConfig, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PATCH', body });
  }

  /**
   * Set cache entry
   */
  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Get cache entry
   */
  private getFromCache(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Set default headers
   */
  setDefaultHeaders(headers: Record<string, string>): void {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers };
  }

  /**
   * Set base URL
   */
  setBaseUrl(url: string): void {
    this.baseUrl = url;
  }
}

// Create singleton instance
export const apiService = new ApiService();

// Specific API methods for portfolio features
export const portfolioApi = {
  getProjects: () => apiService.get('/api/projects', { cache: true }),
  getProject: (id: string) => apiService.get(`/api/projects/${id}`, { cache: true }),
  createProject: (project: any) => apiService.post('/api/projects', project),
  updateProject: (id: string, project: any) => apiService.put(`/api/projects/${id}`, project),
  deleteProject: (id: string) => apiService.delete(`/api/projects/${id}`),
};

// Contact API methods
export const contactApi = {
  submitForm: (formData: any) => apiService.post('/api/contact', formData),
  getContactInfo: () => apiService.get('/api/contact/info', { cache: true }),
};

// About API methods
export const aboutApi = {
  getPersonalInfo: () => apiService.get('/api/about/personal', { cache: true }),
  getSkills: () => apiService.get('/api/about/skills', { cache: true }),
  updatePersonalInfo: (info: any) => apiService.put('/api/about/personal', info),
  updateSkills: (skills: any) => apiService.put('/api/about/skills', skills),
};

export default apiService;
