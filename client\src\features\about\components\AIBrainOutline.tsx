/**
 * AI Brain outline animation component with neural network visualization
 */

import React, { useRef, useEffect } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { ANIMATION_EASINGS, Z_INDEX } from '@/constants';

interface AIBrainOutlineProps {
  autoStart?: boolean;
  startDelay?: number;
  onComplete?: () => void;
  className?: string;
  intensity?: 'subtle' | 'normal' | 'intense';
}

export default function AIBrainOutline({
  autoStart = false,
  startDelay = 0,
  onComplete,
  className = '',
  intensity = 'normal'
}: AIBrainOutlineProps) {
  const brainOutlineRef = useRef<HTMLDivElement>(null);
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  const intensityConfig = {
    subtle: { opacity: 0.08, pulseOpacity: 0.15, pulseCount: 1 },
    normal: { opacity: 0.15, pulseOpacity: 0.25, pulseCount: 3 },
    intense: { opacity: 0.25, pulseOpacity: 0.4, pulseCount: 5 }
  };

  const config = intensityConfig[intensity];

  const startAnimation = React.useCallback(() => {
    try {
      if (!gsap || !isAvailable || !brainOutlineRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) {
        // Set final state for reduced motion
        gsap.set(brainOutlineRef.current, { opacity: config.opacity });
        onComplete?.();
        return;
      }

      const timeline = gsap.timeline({
        onComplete
      });

      // Phase 2: AI Brain Outline Animation
      timeline
        .set(brainOutlineRef.current, { opacity: 0, scale: 0.8 }, 0)
        .to(brainOutlineRef.current, {
          opacity: config.opacity,
          scale: 1,
          duration: 0.8,
          ease: ANIMATION_EASINGS.POWER2_OUT
        }, 0)
        // Pulse sequence
        .to(brainOutlineRef.current, {
          opacity: config.pulseOpacity,
          scale: 1.05,
          filter: 'brightness(1.3)',
          duration: 0.3,
          ease: ANIMATION_EASINGS.POWER2_IN_OUT,
          yoyo: true,
          repeat: config.pulseCount
        }, 1.2)
        .to(brainOutlineRef.current, {
          opacity: config.opacity * 0.7,
          scale: 1,
          filter: 'brightness(1)',
          duration: 0.5,
          ease: ANIMATION_EASINGS.POWER2_OUT
        }, `+=${config.pulseCount * 0.6}`);

    } catch (error) {
      handleError(error as Error);
    }
  }, [gsap, isAvailable, config, onComplete, handleError]);

  // Auto-start effect
  useEffect(() => {
    if (autoStart && isAvailable) {
      const timer = setTimeout(startAnimation, startDelay);
      return () => clearTimeout(timer);
    }
  }, [autoStart, isAvailable, startAnimation, startDelay]);

  // Initialize brain as hidden
  useEffect(() => {
    if (isAvailable && brainOutlineRef.current && gsap) {
      gsap.set(brainOutlineRef.current, { opacity: 0 });
    }
  }, [isAvailable, gsap]);

  return (
    <div
      ref={brainOutlineRef}
      className={`absolute inset-0 opacity-0 pointer-events-none ${className}`}
      style={{ zIndex: Z_INDEX.BACKGROUND + 1 }}
    >
      <svg
        viewBox="0 0 400 400"
        className="w-full h-full max-w-md mx-auto"
        style={{
          filter: 'drop-shadow(0 0 20px #4A90E2)',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)'
        }}
      >
        {/* Brain outline */}
        <path
          d="M200 50 C250 50, 300 80, 320 130 C340 180, 320 220, 300 250 C280 280, 240 300, 200 300 C160 300, 120 280, 100 250 C80 220, 60 180, 80 130 C100 80, 150 50, 200 50 Z"
          stroke="#4A90E2"
          strokeWidth="2"
          fill="none"
          opacity="0.3"
        />
        
        {/* Neural network connections */}
        <g stroke="#4A90E2" strokeWidth="1" opacity="0.2">
          <line x1="120" y1="150" x2="180" y2="180" />
          <line x1="180" y1="180" x2="220" y2="160" />
          <line x1="220" y1="160" x2="280" y2="190" />
          <line x1="150" y1="200" x2="200" y2="220" />
          <line x1="200" y1="220" x2="250" y2="200" />
          <line x1="180" y1="120" x2="220" y2="140" />
          <line x1="140" y1="170" x2="200" y2="190" />
          <line x1="200" y1="190" x2="260" y2="170" />
          <line x1="160" y1="240" x2="220" y2="250" />
          <line x1="220" y1="250" x2="280" y2="230" />
        </g>
        
        {/* Neural nodes */}
        <g fill="#4A90E2" opacity="0.4">
          <circle cx="120" cy="150" r="3" />
          <circle cx="180" cy="180" r="3" />
          <circle cx="220" cy="160" r="3" />
          <circle cx="280" cy="190" r="3" />
          <circle cx="150" cy="200" r="3" />
          <circle cx="200" cy="220" r="3" />
          <circle cx="250" cy="200" r="3" />
          <circle cx="180" cy="120" r="3" />
          <circle cx="220" cy="140" r="3" />
          <circle cx="140" cy="170" r="3" />
          <circle cx="200" cy="190" r="3" />
          <circle cx="260" cy="170" r="3" />
          <circle cx="160" cy="240" r="3" />
          <circle cx="220" cy="250" r="3" />
          <circle cx="280" cy="230" r="3" />
        </g>
        
        {/* Central processing unit indicator */}
        <circle
          cx="200"
          cy="180"
          r="8"
          stroke="#4A90E2"
          strokeWidth="2"
          fill="none"
          opacity="0.6"
        />
        <circle
          cx="200"
          cy="180"
          r="4"
          fill="#4A90E2"
          opacity="0.8"
        />
      </svg>
    </div>
  );
}
