/**
 * Animation library index - provides a clean API for all animation utilities
 */

// Core animation utilities
export {
  AnimationManager,
  createAnimationManager,
  createOptimizedAnimation,
  createStaggeredAnimation
} from './core';

// Animation presets
export {
  ENTRANCE_ANIMATIONS,
  EXIT_ANIMATIONS,
  HOVER_ANIMATIONS,
  LETTER_ANIMATIONS,
  SCROLL_ANIMATIONS,
  TRANSITION_ANIMATIONS,
  LOOP_ANIMATIONS,
  PERFORMANCE_ANIMATIONS,
  getAnimationPreset,
  createAnimationConfig
} from './presets';

// Spaceship animations
export {
  FLIGHT_PATHS,
  HOVER_ANIMATIONS as SPACESHIP_HOVER_ANIMATIONS,
  FLOATING_ANIMATIONS,
  ENTRANCE_ANIMATIONS as SPACESHIP_ENTRANCE_ANIMATIONS,
  LASER_ANIMATIONS,
  createFlightAnimation,
  createHoverEffect,
  createFloatingAnimation,
  createL<PERSON>rAnimation,
  createSpaceshipSequence
} from './spaceship';

// Text animations
export {
  LETTER_ANIMATION_CONFIGS,
  TEXT_ENTRANCE_ANIMATIONS,
  TEXT_TRANSITION_ANIMATIONS,
  createLetterEntranceAnimation,
  createLetterHoverAnimation,
  createLetterClickAnimation,
  resetLetterState,
  createLetterAnimationLoop,
  createTextTransition,
  createTypewriterEffect,
  getAnimationConfigForType
} from './text';

// Re-export types for convenience
export type {
  AnimationConfig,
  LetterAnimationConfig,
  SpaceshipAnimationConfig,
  AnimationSequence,
  AnimationPhase
} from '@/types';
