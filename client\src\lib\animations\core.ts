/**
 * Core animation utilities and abstractions for GSAP
 * Provides consistent animation patterns and performance optimizations
 */

import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import type { 
  AnimationConfig, 
  AnimationSequence, 
  AnimationPhase,
  GSAPTimeline 
} from '@/types';
import { getAccessibilityConfig } from '@/utils';
import { ANIMATION_DURATIONS, ANIMATION_EASINGS } from '@/constants';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

/**
 * Core animation class for managing GSAP animations with cleanup
 */
export class AnimationManager {
  private timelines: GSAPTimeline[] = [];
  private tweens: any[] = [];
  private isDestroyed = false;

  /**
   * Creates a new timeline with automatic tracking for cleanup
   */
  createTimeline(config?: any): any {
    if (this.isDestroyed) return null;

    const timeline = gsap.timeline(config);
    this.timelines.push(timeline as any);
    return timeline;
  }

  /**
   * Creates an animation with accessibility and performance considerations
   */
  animate(
    target: any,
    vars: any,
    config?: AnimationConfig
  ): any {
    if (this.isDestroyed) return null;

    const accessibility = getAccessibilityConfig();
    
    // Respect reduced motion preference
    if (accessibility.reduceMotion) {
      const reducedVars = {
        ...vars,
        duration: 0.1,
        ease: 'none',
      };
      const tween = gsap.set(target, reducedVars);
      this.tweens.push(tween);
      return tween;
    }

    const animationVars = {
      duration: ANIMATION_DURATIONS.NORMAL,
      ease: ANIMATION_EASINGS.POWER2_OUT,
      ...config,
      ...vars,
    };

    const tween = gsap.to(target, animationVars);
    this.tweens.push(tween);
    return tween;
  }

  /**
   * Creates a from animation with accessibility considerations
   */
  animateFrom(
    target: any,
    vars: any,
    config?: AnimationConfig
  ): any {
    if (this.isDestroyed) return null;

    const accessibility = getAccessibilityConfig();
    
    if (accessibility.reduceMotion) {
      const tween = gsap.set(target, vars);
      this.tweens.push(tween);
      return tween;
    }

    const animationVars = {
      duration: ANIMATION_DURATIONS.NORMAL,
      ease: ANIMATION_EASINGS.POWER2_OUT,
      ...config,
      ...vars,
    };

    const tween = gsap.from(target, animationVars);
    this.tweens.push(tween);
    return tween;
  }

  /**
   * Creates a fromTo animation with accessibility considerations
   */
  animateFromTo(
    target: any,
    fromVars: any,
    toVars: any,
    config?: AnimationConfig
  ): any {
    if (this.isDestroyed) return null;

    const accessibility = getAccessibilityConfig();
    
    if (accessibility.reduceMotion) {
      const tween = gsap.set(target, toVars);
      this.tweens.push(tween);
      return tween;
    }

    const animationVars = {
      duration: ANIMATION_DURATIONS.NORMAL,
      ease: ANIMATION_EASINGS.POWER2_OUT,
      ...config,
      ...toVars,
    };

    const tween = gsap.fromTo(target, fromVars, animationVars);
    this.tweens.push(tween);
    return tween;
  }

  /**
   * Creates a scroll-triggered animation
   */
  createScrollAnimation(
    target: any,
    animation: any,
    scrollConfig: any
  ): any {
    if (this.isDestroyed) return null;

    const accessibility = getAccessibilityConfig();
    
    // Disable scroll triggers for reduced motion
    if (accessibility.reduceMotion) {
      // Just set the final state
      gsap.set(target, animation);
      return null;
    }

    const trigger = ScrollTrigger.create({
      trigger: target,
      start: "top 80%",
      end: "bottom 20%",
      toggleActions: "play none none reverse",
      ...scrollConfig,
      animation: gsap.timeline().add(animation)
    });

    return trigger;
  }

  /**
   * Executes an animation sequence with phases
   */
  executeSequence(sequence: AnimationSequence): any {
    if (this.isDestroyed) return null;

    const accessibility = getAccessibilityConfig();

    if (accessibility.reduceMotion) {
      // Execute final states immediately
      sequence.phases.forEach(phase => {
        phase.animations.forEach(animation => {
          // Apply final states without animation
        });
      });
      return null;
    }

    const masterTimeline = this.createTimeline({
      repeat: sequence.loop ? -1 : 0
    });

    let currentTime = 0;
    sequence.phases.forEach(phase => {
      const phaseTimeline = gsap.timeline();

      phase.animations.forEach(animation => {
        // Add animations to phase timeline
        // This would be implemented based on specific animation needs
      });

      if (masterTimeline && masterTimeline.add) {
        masterTimeline.add(phaseTimeline, currentTime + (phase.delay || 0));
      }
      currentTime += phase.duration;
    });

    return masterTimeline;
  }

  /**
   * Kills specific animations or all tracked animations
   */
  kill(target?: any): void {
    if (target) {
      gsap.killTweensOf(target);
    } else {
      // Kill all tracked animations
      this.tweens.forEach(tween => {
        if (tween && tween.kill) {
          tween.kill();
        }
      });
      this.tweens = [];

      this.timelines.forEach(timeline => {
        if (timeline && timeline.kill) {
          timeline.kill();
        }
      });
      this.timelines = [];
    }
  }

  /**
   * Destroys the animation manager and cleans up all animations
   */
  destroy(): void {
    this.kill();
    this.isDestroyed = true;
  }

  /**
   * Checks if GSAP is available
   */
  get isAvailable(): boolean {
    return typeof window !== 'undefined' && !!gsap;
  }
}

/**
 * Creates a new animation manager instance
 */
export function createAnimationManager(): AnimationManager {
  return new AnimationManager();
}

/**
 * Utility function to create performance-optimized animations
 */
export function createOptimizedAnimation(
  target: any,
  vars: any,
  config?: AnimationConfig & { 
    useTransform?: boolean;
    willChange?: string;
  }
): any {
  const { useTransform = true, willChange, ...animationConfig } = config || {};
  
  // Set will-change for performance
  if (willChange && target.style) {
    target.style.willChange = willChange;
  }

  // Prefer transform properties for better performance
  const optimizedVars = useTransform ? {
    ...vars,
    force3D: true,
  } : vars;

  const animation = gsap.to(target, {
    ...optimizedVars,
    ...animationConfig,
    onComplete: () => {
      // Clean up will-change after animation
      if (willChange && target.style) {
        target.style.willChange = 'auto';
      }
      animationConfig.onComplete?.();
    }
  });

  return animation;
}

/**
 * Utility function for staggered animations
 */
export function createStaggeredAnimation(
  targets: any[],
  vars: any,
  config?: AnimationConfig & { stagger?: number }
): any {
  const { stagger = 0.1, ...animationConfig } = config || {};
  
  return gsap.to(targets, {
    ...vars,
    ...animationConfig,
    stagger
  });
}
