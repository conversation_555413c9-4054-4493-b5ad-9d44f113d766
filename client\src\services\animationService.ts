/**
 * Animation Service
 * Centralized animation utilities and common patterns
 * Works with the existing useGSAP hook system
 */

import { getAccessibilityConfig } from '@/utils';

export interface AnimationConfig {
  duration?: number;
  ease?: string;
  delay?: number;
  stagger?: number;
  repeat?: number;
  yoyo?: boolean;
  onComplete?: () => void;
  onStart?: () => void;
}

export interface ScrollTriggerConfig {
  trigger: string | Element;
  start?: string;
  end?: string;
  scrub?: boolean | number;
  pin?: boolean;
  snap?: boolean | number | object;
  onEnter?: () => void;
  onLeave?: () => void;
  onEnterBack?: () => void;
  onLeaveBack?: () => void;
}

export interface ParallaxConfig {
  speed?: number;
  direction?: 'vertical' | 'horizontal';
  scale?: boolean;
  rotation?: boolean;
}

/**
 * Animation utility functions
 * These work with any GSAP instance passed to them
 */
export const animationService = {
  /**
   * Check if user prefers reduced motion
   */
  checkReducedMotion(): boolean {
    const { reduceMotion } = getAccessibilityConfig();
    return reduceMotion;
  },

  /**
   * Get default animation config with accessibility considerations
   */
  getDefaultConfig(config: AnimationConfig = {}): AnimationConfig {
    const reduceMotion = this.checkReducedMotion();

    return {
      duration: reduceMotion ? 0 : (config.duration || 0.8),
      ease: config.ease || 'power2.out',
      delay: reduceMotion ? 0 : (config.delay || 0),
      stagger: reduceMotion ? 0 : (config.stagger || 0),
      ...config,
    };
  },

  /**
   * Create fade in animation
   */
  createFadeIn(gsap: any, target: any, config: AnimationConfig = {}) {
    if (this.checkReducedMotion()) {
      gsap.set(target, { opacity: 1 });
      return null;
    }

    const finalConfig = this.getDefaultConfig(config);
    return gsap.fromTo(target,
      { opacity: 0 },
      {
        opacity: 1,
        ...finalConfig,
      }
    );
  },

  /**
   * Create slide in animation
   */
  createSlideIn(
    gsap: any,
    target: any,
    direction: 'up' | 'down' | 'left' | 'right' = 'up',
    config: AnimationConfig = {}
  ) {
    if (this.checkReducedMotion()) {
      gsap.set(target, { opacity: 1, x: 0, y: 0 });
      return null;
    }

    const distance = 100;
    const fromVars: any = { opacity: 0 };
    const toVars: any = { opacity: 1, x: 0, y: 0 };

    switch (direction) {
      case 'up':
        fromVars.y = distance;
        break;
      case 'down':
        fromVars.y = -distance;
        break;
      case 'left':
        fromVars.x = distance;
        break;
      case 'right':
        fromVars.x = -distance;
        break;
    }

    const finalConfig = this.getDefaultConfig(config);
    return gsap.fromTo(target, fromVars, {
      ...toVars,
      ...finalConfig,
    });
  },

  /**
   * Create scale animation
   */
  createScale(
    gsap: any,
    target: any,
    fromScale: number = 0,
    toScale: number = 1,
    config: AnimationConfig = {}
  ) {
    if (this.checkReducedMotion()) {
      gsap.set(target, { scale: toScale, opacity: 1 });
      return null;
    }

    const finalConfig = this.getDefaultConfig({
      ease: 'back.out(1.7)',
      ...config,
    });

    return gsap.fromTo(target,
      { scale: fromScale, opacity: 0 },
      {
        scale: toScale,
        opacity: 1,
        ...finalConfig,
      }
    );
  },

  /**
   * Create stagger animation
   */
  createStagger(
    gsap: any,
    targets: any,
    animationType: 'fadeIn' | 'slideIn' | 'scale',
    config: AnimationConfig & { direction?: 'up' | 'down' | 'left' | 'right' } = {}
  ) {
    const staggerConfig = {
      ...config,
      stagger: config.stagger || 0.1,
    };

    switch (animationType) {
      case 'fadeIn':
        return this.createFadeIn(gsap, targets, staggerConfig);
      case 'slideIn':
        return this.createSlideIn(gsap, targets, config.direction || 'up', staggerConfig);
      case 'scale':
        return this.createScale(gsap, targets, 0, 1, staggerConfig);
      default:
        return null;
    }
  },

  /**
   * Create hover animation helper
   */
  createHoverAnimation(
    gsap: any,
    target: any,
    hoverVars: any,
    config: AnimationConfig = {}
  ) {
    if (this.checkReducedMotion()) {
      return { enter: () => {}, leave: () => {} };
    }

    const duration = config.duration || 0.3;
    const ease = config.ease || 'power2.out';

    return {
      enter: () => gsap.to(target, { ...hoverVars, duration, ease }),
      leave: () => gsap.to(target, {
        scale: 1,
        rotation: 0,
        x: 0,
        y: 0,
        duration,
        ease
      }),
    };
  },

  /**
   * Animation presets for common patterns
   */
  presets: {
    fadeInUp: (gsap: any, target: any, config?: AnimationConfig) =>
      animationService.createSlideIn(gsap, target, 'up', config),

    fadeInDown: (gsap: any, target: any, config?: AnimationConfig) =>
      animationService.createSlideIn(gsap, target, 'down', config),

    fadeInLeft: (gsap: any, target: any, config?: AnimationConfig) =>
      animationService.createSlideIn(gsap, target, 'left', config),

    fadeInRight: (gsap: any, target: any, config?: AnimationConfig) =>
      animationService.createSlideIn(gsap, target, 'right', config),

    scaleIn: (gsap: any, target: any, config?: AnimationConfig) =>
      animationService.createScale(gsap, target, 0, 1, config),

    pulse: (gsap: any, target: any, config?: AnimationConfig) => {
      if (animationService.checkReducedMotion()) return null;

      return gsap.to(target, {
        scale: 1.1,
        duration: config?.duration || 1,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        ...config,
      });
    },
  },
};

export default animationService;
