/**
 * Services Module Exports
 * Centralized exports for all shared services
 */

// API Service
export { default as apiService, portfolioApi, contactApi, aboutApi } from './apiService';
export type { ApiResponse, ApiRequestConfig } from './apiService';

// State Manager
export { 
  default as stateManager,
  createStateManager,
  useGlobalState,
  useEnhancedState,
  uiStateManager,
  userPreferencesManager,
} from './stateManager';
export type { 
  StateManagerConfig,
  UIState,
  UserPreferences,
} from './stateManager';

// Animation Service
export { default as animationService } from './animationService';
export type { 
  AnimationConfig,
  ScrollTriggerConfig,
  ParallaxConfig,
} from './animationService';

// Validation Service
export { default as validationService } from './validationService';
export type { 
  ValidationRule,
  ValidationSchema,
  ValidationResult,
  ValidationOptions,
} from './validationService';
