/**
 * Reusable animated text component with various reveal effects
 */

import React, { useRef, useEffect, useCallback } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { ANIMATION_DURATIONS, ANIMATION_EASINGS } from '@/constants';

type AnimationType = 
  | 'fadeUp' 
  | 'fadeDown' 
  | 'fadeLeft' 
  | 'fadeRight' 
  | 'scale' 
  | 'typewriter' 
  | 'splitWords' 
  | 'splitChars'
  | 'glitch';

interface AnimatedTextProps {
  children: React.ReactNode;
  animation?: AnimationType;
  duration?: number;
  delay?: number;
  stagger?: number;
  autoStart?: boolean;
  triggerOnScroll?: boolean;
  triggerStart?: string;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
  onComplete?: () => void;
  onStart?: () => void;
  disabled?: boolean;
}

export default function AnimatedText({
  children,
  animation = 'fadeUp',
  duration = ANIMATION_DURATIONS.SLOW,
  delay = 0,
  stagger = 0.05,
  autoStart = false,
  triggerOnScroll = true,
  triggerStart = 'top 80%',
  className = '',
  as: Component = 'div',
  onComplete,
  onStart,
  disabled = false
}: AnimatedTextProps) {
  const elementRef = useRef<HTMLElement>(null);
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  // Animation configurations
  const getAnimationConfig = useCallback((type: AnimationType) => {
    const configs = {
      fadeUp: {
        from: { opacity: 0, y: 30 },
        to: { opacity: 1, y: 0 }
      },
      fadeDown: {
        from: { opacity: 0, y: -30 },
        to: { opacity: 1, y: 0 }
      },
      fadeLeft: {
        from: { opacity: 0, x: -30 },
        to: { opacity: 1, x: 0 }
      },
      fadeRight: {
        from: { opacity: 0, x: 30 },
        to: { opacity: 1, x: 0 }
      },
      scale: {
        from: { opacity: 0, scale: 0.8 },
        to: { opacity: 1, scale: 1 }
      },
      typewriter: {
        from: { width: 0 },
        to: { width: 'auto' }
      },
      splitWords: {
        from: { opacity: 0, y: 20 },
        to: { opacity: 1, y: 0 }
      },
      splitChars: {
        from: { opacity: 0, y: 10, rotationX: 90 },
        to: { opacity: 1, y: 0, rotationX: 0 }
      },
      glitch: {
        from: { opacity: 0 },
        to: { opacity: 1 }
      }
    };

    return configs[type] || configs.fadeUp;
  }, []);

  // Split text into words or characters
  const splitText = useCallback((element: HTMLElement, splitType: 'words' | 'chars') => {
    const text = element.textContent || '';
    const splits = splitType === 'words' ? text.split(' ') : text.split('');
    
    element.innerHTML = splits
      .map((item, index) => 
        `<span class="split-${splitType}" style="display: inline-block;">${
          splitType === 'words' ? item + (index < splits.length - 1 ? '&nbsp;' : '') : item
        }</span>`
      )
      .join('');

    return element.querySelectorAll(`.split-${splitType}`);
  }, []);

  // Main animation function
  const startAnimation = useCallback(() => {
    try {
      if (disabled || !isAvailable || !gsap || !elementRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) {
        // Set final state immediately for reduced motion
        gsap.set(elementRef.current, { opacity: 1 });
        onComplete?.();
        return;
      }

      onStart?.();

      const config = getAnimationConfig(animation);
      const timeline = gsap.timeline({
        onComplete
      });

      // Handle different animation types
      switch (animation) {
        case 'splitWords': {
          const words = splitText(elementRef.current, 'words');
          timeline.fromTo(words, config.from, {
            ...config.to,
            duration,
            ease: ANIMATION_EASINGS.POWER2_OUT,
            stagger,
            delay
          });
          break;
        }

        case 'splitChars': {
          const chars = splitText(elementRef.current, 'chars');
          timeline.fromTo(chars, config.from, {
            ...config.to,
            duration,
            ease: ANIMATION_EASINGS.BACK_OUT,
            stagger,
            delay
          });
          break;
        }

        case 'typewriter': {
          // Set up typewriter effect
          elementRef.current.style.overflow = 'hidden';
          elementRef.current.style.whiteSpace = 'nowrap';
          timeline.fromTo(elementRef.current, config.from, {
            ...config.to,
            duration: duration * 2, // Typewriter is typically slower
            ease: 'none',
            delay
          });
          break;
        }

        case 'glitch': {
          // Glitch effect with multiple phases
          timeline
            .fromTo(elementRef.current, config.from, {
              ...config.to,
              duration: 0.1,
              delay
            })
            .to(elementRef.current, {
              x: 2,
              skewX: 2,
              duration: 0.05,
              ease: 'power2.inOut',
              yoyo: true,
              repeat: 3
            })
            .to(elementRef.current, {
              x: 0,
              skewX: 0,
              duration: 0.1
            });
          break;
        }

        default: {
          // Standard animations
          timeline.fromTo(elementRef.current, config.from, {
            ...config.to,
            duration,
            ease: ANIMATION_EASINGS.POWER2_OUT,
            delay
          });
        }
      }

    } catch (error) {
      handleError(error as Error);
    }
  }, [
    disabled,
    isAvailable,
    gsap,
    animation,
    duration,
    delay,
    stagger,
    getAnimationConfig,
    splitText,
    onComplete,
    onStart,
    handleError
  ]);

  // Initialize animation
  useEffect(() => {
    if (!isAvailable || !elementRef.current) return;

    // Hide element initially
    gsap?.set(elementRef.current, { opacity: 0 });

    if (autoStart && !triggerOnScroll) {
      const timer = setTimeout(startAnimation, 100);
      return () => clearTimeout(timer);
    }

    if (triggerOnScroll) {
      const trigger = gsap?.timeline().to({}, {
        duration: 0,
        scrollTrigger: {
          trigger: elementRef.current,
          start: triggerStart,
          once: true,
          onEnter: startAnimation
        }
      });

      return () => {
        if (trigger) trigger.kill();
      };
    }
  }, [isAvailable, gsap, autoStart, triggerOnScroll, triggerStart, startAnimation]);

  return React.createElement(
    Component,
    {
      ref: elementRef,
      className
    },
    children
  );
}

// Preset components for common use cases
export const AnimatedHeading = (props: Omit<AnimatedTextProps, 'as'>) => (
  <AnimatedText {...props} as="h2" animation="splitWords" />
);

export const AnimatedParagraph = (props: Omit<AnimatedTextProps, 'as'>) => (
  <AnimatedText {...props} as="p" animation="fadeUp" />
);

export const TypewriterText = (props: Omit<AnimatedTextProps, 'animation'>) => (
  <AnimatedText {...props} animation="typewriter" />
);

export const GlitchText = (props: Omit<AnimatedTextProps, 'animation'>) => (
  <AnimatedText {...props} animation="glitch" />
);
