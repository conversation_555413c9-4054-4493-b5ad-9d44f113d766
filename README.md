# Portfolio Website

A modern, high-performance portfolio website built with React, TypeScript, and feature-based architecture. Features comprehensive code splitting, advanced animations, and 100% test coverage.

## 🚀 Features

### Core Features
- 🎨 **Modern Design**: Responsive, mobile-first design with dark theme
- ⚡ **High Performance**: Code splitting, lazy loading, and bundle optimization
- 🎭 **Advanced Animations**: GSAP-powered animations with accessibility support
- 🧩 **Feature Modules**: Self-contained feature modules for scalability
- 🔒 **Type Safety**: Full TypeScript coverage with strict type checking
- 🧪 **Comprehensive Testing**: 146 tests with 100% coverage
- ♿ **Accessibility**: WCAG compliant with reduced motion support

### Performance Optimizations
- **React.lazy()** code splitting for all major components
- **Intelligent preloading** based on user interaction patterns
- **Bundle optimization** with chunk analysis and recommendations
- **Adaptive loading** based on device capabilities and network speed
- **Service-level caching** for API calls and expensive operations

### Architecture Highlights
- **Feature-based organization** with dedicated modules for portfolio, contact, and about sections
- **Shared services** for API communication, state management, validation, and animations
- **Custom hooks** for reusable logic and state management
- **Comprehensive documentation** with architectural guides and API references

## 🛠️ Tech Stack

### Frontend
- **React 18** - Modern React with concurrent features
- **TypeScript** - Full type safety and developer experience
- **Vite** - Fast build tool with HMR
- **Tailwind CSS** - Utility-first CSS framework
- **GSAP** - Professional-grade animations

### Testing & Quality
- **Vitest** - Fast unit testing framework
- **React Testing Library** - Component testing utilities
- **ESLint** - Code linting and style enforcement
- **Prettier** - Code formatting
- **TypeScript** - Static type checking

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd portfolio-website-replit/client
```

2. **Install dependencies**
```bash
npm install
```

3. **Start development server**
```bash
npm run dev
```

4. **Open in browser**
Navigate to [http://localhost:5000](http://localhost:5000)

### Development Commands

```bash
# Development
npm run dev              # Start dev server with HMR
npm run build           # Build for production
npm run preview         # Preview production build

# Testing
npm test               # Run all tests (146 tests)
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Generate coverage report

# Code Quality
npm run lint           # Run ESLint
npm run type-check     # TypeScript type checking
```

## 📁 Project Structure

```
client/
├── src/
│   ├── components/           # Shared UI components
│   │   ├── LazyComponents.tsx    # Code-split component wrappers
│   │   ├── LoadingSpinner.tsx    # Loading indicators
│   │   └── ...
│   ├── features/            # Feature modules
│   │   ├── portfolio/           # Portfolio feature
│   │   ├── contact/            # Contact feature
│   │   └── about/              # About feature
│   ├── services/            # Shared services
│   │   ├── apiService.ts        # HTTP client
│   │   ├── stateManager.ts      # State management
│   │   ├── animationService.ts  # Animation utilities
│   │   ├── validationService.ts # Validation utilities
│   │   └── bundleOptimizationService.ts
│   ├── hooks/               # Shared custom hooks
│   │   ├── useGSAP.ts          # GSAP integration
│   │   ├── useLazyLoading.ts   # Lazy loading utilities
│   │   └── ...
│   ├── pages/               # Page components
│   ├── utils/               # Shared utilities
│   └── __tests__/           # Test files (146 tests)
├── docs/                    # Documentation
│   ├── ARCHITECTURE.md         # Architecture overview
│   ├── FEATURE_MODULES.md      # Feature module guide
│   ├── HOOKS_GUIDE.md          # Custom hooks documentation
│   └── SERVICES_GUIDE.md       # Services documentation
└── public/                  # Static assets
```

## 🧪 Testing

### Test Coverage
- **146 tests** across all modules
- **100% coverage** of critical functionality
- **Unit tests** for components, hooks, and utilities
- **Integration tests** for feature interactions
- **Service tests** for API and business logic

### Running Tests
```bash
# Run all tests
npm test

# Watch mode for development
npm run test:watch

# Run specific test file
npm test -- portfolio
```

## 📚 Documentation

### Architecture Guides
- **[Architecture Overview](./client/docs/ARCHITECTURE.md)** - System design and principles
- **[Feature Modules](./client/docs/FEATURE_MODULES.md)** - Feature-based organization guide
- **[Custom Hooks](./client/docs/HOOKS_GUIDE.md)** - Hook usage and patterns
- **[Services Guide](./client/docs/SERVICES_GUIDE.md)** - Shared services documentation

## 🎯 Performance Metrics

### Bundle Optimization
- **Initial bundle size**: Reduced by ~200KB through code splitting
- **Lazy chunks**: 8 separate chunks for major features
- **Preloading**: Intelligent preloading reduces perceived load time
- **Cache efficiency**: Service-level caching improves repeat visits

## 🤝 Contributing

### Development Process
1. **Fork** the repository
2. **Create** a feature branch
3. **Implement** your changes with tests
4. **Ensure** all tests pass (`npm test`)
5. **Check** code quality (`npm run lint`)
6. **Submit** a Pull Request

### Code Standards
- **TypeScript**: Full type coverage required
- **Testing**: Maintain 100% test coverage
- **Documentation**: Update docs for new features
- **Performance**: Consider bundle size impact
- **Accessibility**: Follow WCAG guidelines

## 📄 License

This project is licensed under the MIT License.