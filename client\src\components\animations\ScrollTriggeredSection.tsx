/**
 * Reusable scroll-triggered section component with customizable animations
 */

import React, { useRef, useEffect, useCallback } from 'react';
import { useScrollTrigger } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';

interface ScrollTriggeredSectionProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  triggerStart?: string;
  triggerEnd?: string;
  once?: boolean;
  onEnter?: () => void;
  onLeave?: () => void;
  onEnterBack?: () => void;
  onLeaveBack?: () => void;
  disabled?: boolean;
  role?: string;
  'aria-labelledby'?: string;
}

export default function ScrollTriggeredSection({
  children,
  className = '',
  id,
  triggerStart = 'top 80%',
  triggerEnd = 'bottom 20%',
  once = false,
  onEnter,
  onLeave,
  onEnterBack,
  onLeaveBack,
  disabled = false,
  role,
  'aria-labelledby': ariaLabelledBy
}: ScrollTriggeredSectionProps) {
  const sectionRef = useRef<HTMLElement>(null);
  const { createScrollTrigger } = useScrollTrigger();
  const { handleError } = useErrorHandler();

  const initializeScrollTrigger = useCallback(() => {
    try {
      if (disabled || !sectionRef.current) return;

      const accessibility = getAccessibilityConfig();
      
      // Skip scroll triggers for reduced motion if no callbacks provided
      if (accessibility.reduceMotion && !onEnter && !onLeave && !onEnterBack && !onLeaveBack) {
        return;
      }

      const trigger = createScrollTrigger({
        trigger: sectionRef.current,
        start: triggerStart,
        end: triggerEnd,
        once,
        onEnter: () => {
          try {
            onEnter?.();
          } catch (error) {
            handleError(error as Error);
          }
        },
        onLeave: () => {
          try {
            onLeave?.();
          } catch (error) {
            handleError(error as Error);
          }
        },
        onEnterBack: () => {
          try {
            onEnterBack?.();
          } catch (error) {
            handleError(error as Error);
          }
        },
        onLeaveBack: () => {
          try {
            onLeaveBack?.();
          } catch (error) {
            handleError(error as Error);
          }
        }
      });

      return () => {
        if (trigger) trigger.kill();
      };
    } catch (error) {
      handleError(error as Error);
    }
  }, [
    disabled,
    triggerStart,
    triggerEnd,
    once,
    onEnter,
    onLeave,
    onEnterBack,
    onLeaveBack,
    createScrollTrigger,
    handleError
  ]);

  useEffect(() => {
    const cleanup = initializeScrollTrigger();
    return cleanup;
  }, [initializeScrollTrigger]);

  return (
    <section
      ref={sectionRef}
      className={className}
      id={id}
      role={role}
      aria-labelledby={ariaLabelledBy}
    >
      {children}
    </section>
  );
}

// Higher-order component version for easier composition
export function withScrollTrigger<T extends object>(
  WrappedComponent: React.ComponentType<T>,
  scrollTriggerProps: Partial<ScrollTriggeredSectionProps> = {}
) {
  return function ScrollTriggeredWrapper(props: T) {
    return (
      <ScrollTriggeredSection {...scrollTriggerProps}>
        <WrappedComponent {...props} />
      </ScrollTriggeredSection>
    );
  };
}

// Hook for manual scroll trigger control
export function useScrollTriggeredAnimation(
  triggerOptions: {
    start?: string;
    end?: string;
    once?: boolean;
  } = {}
) {
  const elementRef = useRef<HTMLElement>(null);
  const { createScrollTrigger } = useScrollTrigger();
  const { handleError } = useErrorHandler();

  const createTrigger = useCallback((callbacks: {
    onEnter?: () => void;
    onLeave?: () => void;
    onEnterBack?: () => void;
    onLeaveBack?: () => void;
  }) => {
    try {
      if (!elementRef.current) return;

      const {
        start = 'top 80%',
        end = 'bottom 20%',
        once = false
      } = triggerOptions;

      return createScrollTrigger({
        trigger: elementRef.current,
        start,
        end,
        once,
        ...callbacks
      });
    } catch (error) {
      handleError(error as Error);
    }
  }, [triggerOptions, createScrollTrigger, handleError]);

  return {
    elementRef,
    createTrigger
  };
}
