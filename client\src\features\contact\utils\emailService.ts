/**
 * Email Service Utilities
 * Email sending and template management for contact forms
 */

import { ContactFormData, EmailServiceResponse, EmailTemplateData } from '../types';

export const emailService = {
  /**
   * Send contact email using mailto (fallback method)
   */
  sendContactEmail: async (formData: ContactFormData): Promise<EmailServiceResponse> => {
    try {
      // Sanitize and prepare email data
      const emailData = emailService.prepareEmailData(formData);
      
      // Create mailto URL
      const mailtoUrl = emailService.createMailtoUrl(emailData);
      
      // Open mailto link
      window.location.href = mailtoUrl;
      
      // Log form submission for analytics
      console.log('Contact form submitted:', formData);
      
      return {
        success: true,
        message: 'Email client opened successfully',
      };
    } catch (error) {
      console.error('Email service error:', error);
      return {
        success: false,
        message: 'Failed to open email client',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },

  /**
   * Prepare email data from form data
   */
  prepareEmailData: (formData: ContactFormData): EmailTemplateData => {
    return {
      from_name: formData.name,
      from_email: formData.email,
      message: emailService.formatEmailMessage(formData),
      subject: formData.subject || `Contact Form Submission from ${formData.name}`,
      to_email: '<EMAIL>', // Replace with actual email
    };
  },

  /**
   * Format email message with form data
   */
  formatEmailMessage: (formData: ContactFormData): string => {
    let message = `Name: ${formData.name}\n`;
    message += `Email: ${formData.email}\n`;
    
    if (formData.phone) {
      message += `Phone: ${formData.phone}\n`;
    }
    
    if (formData.company) {
      message += `Company: ${formData.company}\n`;
    }
    
    message += `\nMessage:\n${formData.message}`;
    
    return message;
  },

  /**
   * Create mailto URL
   */
  createMailtoUrl: (emailData: EmailTemplateData): string => {
    const params = new URLSearchParams({
      subject: emailData.subject || '',
      body: emailData.message,
    });
    
    return `mailto:${emailData.to_email}?${params.toString()}`;
  },

  /**
   * Validate email configuration
   */
  validateEmailConfig: (): boolean => {
    // Basic validation - in a real app, you'd check API keys, etc.
    return true;
  },

  /**
   * Send email using EmailJS (if configured)
   */
  sendEmailJS: async (formData: ContactFormData): Promise<EmailServiceResponse> => {
    try {
      // This would integrate with EmailJS or similar service
      // For now, we'll simulate the API call
      
      const emailData = emailService.prepareEmailData(formData);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate success/failure
      const isSuccess = Math.random() > 0.1; // 90% success rate
      
      if (isSuccess) {
        return {
          success: true,
          message: 'Email sent successfully',
        };
      } else {
        throw new Error('Email service temporarily unavailable');
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to send email',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },

  /**
   * Send email using Netlify Forms (if deployed on Netlify)
   */
  sendNetlifyForm: async (formData: ContactFormData): Promise<EmailServiceResponse> => {
    try {
      const response = await fetch('/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          'form-name': 'contact',
          ...formData,
        }).toString(),
      });

      if (response.ok) {
        return {
          success: true,
          message: 'Form submitted successfully',
        };
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to submit form',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },

  /**
   * Auto-detect and use best available email service
   */
  sendEmailAuto: async (formData: ContactFormData): Promise<EmailServiceResponse> => {
    // Try services in order of preference
    const services = [
      emailService.sendNetlifyForm,
      emailService.sendEmailJS,
      emailService.sendContactEmail, // Fallback to mailto
    ];

    for (const service of services) {
      try {
        const result = await service(formData);
        if (result.success) {
          return result;
        }
      } catch (error) {
        console.warn('Email service failed, trying next:', error);
      }
    }

    return {
      success: false,
      message: 'All email services failed',
      error: 'No available email service',
    };
  },

  /**
   * Track email analytics
   */
  trackEmailEvent: (event: string, data?: any) => {
    try {
      // In a real app, this would send to analytics service
      console.log('Email event:', event, data);
      
      // Store in localStorage for basic tracking
      const events = JSON.parse(localStorage.getItem('email_events') || '[]');
      events.push({
        event,
        data,
        timestamp: new Date().toISOString(),
      });
      
      // Keep only last 100 events
      if (events.length > 100) {
        events.splice(0, events.length - 100);
      }
      
      localStorage.setItem('email_events', JSON.stringify(events));
    } catch (error) {
      console.warn('Failed to track email event:', error);
    }
  },
};
