/**
 * Text animation utilities and configurations
 * Provides reusable animation patterns for text elements
 */

import { gsap } from 'gsap';
import type { LetterAnimationConfig, AnimationConfig, GSAPTimeline } from '@/types';
import { getAccessibilityConfig, randomChoice } from '@/utils';
import { 
  ANIMATION_DURATIONS, 
  ANIMATION_EASINGS, 
  COLOR_VARIANTS,
  LETTER_ANIMATION_TYPES 
} from '@/constants';

/**
 * Letter animation configurations by type
 */
export const LETTER_ANIMATION_CONFIGS: Record<string, LetterAnimationConfig> = {
  glow: {
    type: 'glow',
    duration: ANIMATION_DURATIONS.NORMAL,
    ease: ANIMATION_EASINGS.POWER2_OUT,
    color: COLOR_VARIANTS.BLUE,
  },
  
  rotation: {
    type: 'rotation',
    duration: ANIMATION_DURATIONS.SLOW,
    ease: ANIMATION_EASINGS.POWER2_IN_OUT,
    rotation: 360,
  },
  
  bounce: {
    type: 'bounce',
    duration: ANIMATION_DURATIONS.NORMAL,
    ease: ANIMATION_EASINGS.BOUNCE_OUT,
    scale: 1.2,
  },
  
  flip: {
    type: 'flip',
    duration: ANIMATION_DURATIONS.SLOW,
    ease: ANIMATION_EASINGS.POWER2_IN_OUT,
    rotation: 180,
  },
  
  pulse: {
    type: 'pulse',
    duration: ANIMATION_DURATIONS.FAST,
    ease: ANIMATION_EASINGS.POWER2_OUT,
    scale: 1.1,
    repeat: 1,
    yoyo: true,
  }
};

/**
 * Text entrance animations
 */
export const TEXT_ENTRANCE_ANIMATIONS = {
  typewriter: {
    from: { width: 0, opacity: 1 },
    to: { width: '100%', duration: ANIMATION_DURATIONS.VERY_SLOW, ease: 'none' }
  },
  
  fadeInWords: {
    from: { opacity: 0, y: 20 },
    to: { opacity: 1, y: 0, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  slideInLetters: {
    from: { opacity: 0, y: 100, rotationX: 90, transformOrigin: 'center bottom' },
    to: { opacity: 1, y: 0, rotationX: 0, duration: ANIMATION_DURATIONS.SLOW, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  scaleInLetters: {
    from: { opacity: 0, scale: 0, rotation: 180 },
    to: { opacity: 1, scale: 1, rotation: 0, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.BACK_OUT }
  }
} as const;

/**
 * Text transition animations
 */
export const TEXT_TRANSITION_ANIMATIONS = {
  blur: {
    exit: { opacity: 0, y: -10, filter: "blur(4px)", duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.POWER2_IN },
    enter: { 
      opacity: 1, 
      y: 0, 
      scale: 1, 
      filter: "blur(0px)", 
      textShadow: `0 0 20px rgba(255, 255, 255, 0.3), 0 0 40px ${COLOR_VARIANTS.BLUE}33`,
      duration: ANIMATION_DURATIONS.NORMAL, 
      ease: ANIMATION_EASINGS.POWER2_OUT 
    },
    cleanup: { textShadow: "none", duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  slide: {
    exit: { opacity: 0, x: -50, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_IN },
    enter: { opacity: 1, x: 0, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.POWER2_OUT }
  },
  
  scale: {
    exit: { opacity: 0, scale: 0.8, duration: ANIMATION_DURATIONS.FAST, ease: ANIMATION_EASINGS.POWER2_IN },
    enter: { opacity: 1, scale: 1, duration: ANIMATION_DURATIONS.NORMAL, ease: ANIMATION_EASINGS.BACK_OUT }
  }
} as const;

/**
 * Creates letter entrance animation with stagger
 */
export function createLetterEntranceAnimation(
  letters: NodeListOf<Element> | Element[],
  config: AnimationConfig & { stagger?: number } = {}
): any {
  const accessibility = getAccessibilityConfig();
  
  if (accessibility.reduceMotion) {
    // Just set final state for reduced motion
    Array.from(letters).forEach(letter => {
      gsap.set(letter, {
        opacity: 1,
        y: 0,
        rotation: 0,
        scale: 1,
      });
    });
    return null;
  }

  const {
    duration = ANIMATION_DURATIONS.NORMAL,
    delay = 0,
    ease = ANIMATION_EASINGS.POWER2_OUT,
    stagger = 0.1,
    ...vars
  } = config;

  return gsap.fromTo(letters,
    {
      opacity: 0,
      y: 100,
      rotationX: 90,
      transformOrigin: 'center bottom',
    },
    {
      opacity: 1,
      y: 0,
      rotationX: 0,
      duration,
      ease,
      delay,
      stagger,
      ...vars,
    }
  );
}

/**
 * Creates letter hover animation
 */
export function createLetterHoverAnimation(
  letter: Element,
  config: LetterAnimationConfig
): any {
  const accessibility = getAccessibilityConfig();
  
  if (accessibility.reduceMotion) {
    return null;
  }

  switch (config.type) {
    case 'glow':
      return gsap.to(letter, {
        color: config.color || COLOR_VARIANTS.BLUE,
        textShadow: `0 0 20px ${config.color || COLOR_VARIANTS.BLUE}`,
        duration: config.duration,
        ease: config.ease
      });
      
    case 'rotation':
      return gsap.to(letter, {
        rotation: config.rotation || 360,
        duration: config.duration,
        ease: config.ease
      });
      
    case 'bounce':
      return gsap.to(letter, {
        y: -20,
        scale: config.scale || 1.2,
        duration: config.duration,
        ease: config.ease,
        yoyo: true,
        repeat: 1
      });
      
    case 'flip':
      return gsap.to(letter, {
        rotationY: config.rotation || 180,
        duration: config.duration,
        ease: config.ease
      });
      
    case 'pulse':
      return gsap.to(letter, {
        scale: config.scale || 1.1,
        duration: config.duration,
        ease: config.ease,
        repeat: config.repeat || 1,
        yoyo: config.yoyo !== false
      });
      
    default:
      return null;
  }
}

/**
 * Creates letter click animation
 */
export function createLetterClickAnimation(letter: Element): any {
  const accessibility = getAccessibilityConfig();
  
  if (accessibility.reduceMotion) {
    return null;
  }

  const timeline = gsap.timeline();
  
  timeline
    .to(letter, {
      scale: 1.3,
      rotation: 15,
      color: COLOR_VARIANTS.PINK,
      duration: 0.1,
      ease: ANIMATION_EASINGS.POWER2_OUT
    })
    .to(letter, {
      scale: 1,
      rotation: 0,
      color: COLOR_VARIANTS.WHITE,
      duration: 0.3,
      ease: ANIMATION_EASINGS.BOUNCE_OUT
    });
    
  return timeline;
}

/**
 * Resets letter to original state
 */
export function resetLetterState(letter: Element): any {
  return gsap.to(letter, {
    scale: 1,
    rotation: 0,
    rotationY: 0,
    y: 0,
    color: COLOR_VARIANTS.WHITE,
    textShadow: 'none',
    duration: ANIMATION_DURATIONS.FAST,
    ease: ANIMATION_EASINGS.POWER2_OUT
  });
}

/**
 * Creates continuous letter animation loop
 */
export function createLetterAnimationLoop(
  letters: NodeListOf<Element> | Element[],
  interval: number = 5000
): () => void {
  const accessibility = getAccessibilityConfig();
  
  if (accessibility.reduceMotion) {
    return () => {}; // Return empty cleanup function
  }

  let timeoutId: NodeJS.Timeout;
  
  const animateRandomLetter = () => {
    if (letters.length === 0) return;
    
    const randomLetter = letters[Math.floor(Math.random() * letters.length)];
    const animationType = randomChoice(LETTER_ANIMATION_TYPES);
    const config = LETTER_ANIMATION_CONFIGS[animationType];
    
    if (config) {
      const animation = createLetterHoverAnimation(randomLetter, config);
      
      if (animation) {
        animation.eventCallback('onComplete', () => {
          resetLetterState(randomLetter);
        });
      }
    }
    
    // Schedule next animation
    timeoutId = setTimeout(animateRandomLetter, interval);
  };
  
  // Start the loop
  timeoutId = setTimeout(animateRandomLetter, interval);
  
  // Return cleanup function
  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
  };
}

/**
 * Creates text transition animation
 */
export function createTextTransition(
  element: HTMLElement,
  newText: string,
  type: keyof typeof TEXT_TRANSITION_ANIMATIONS = 'blur'
): any {
  const accessibility = getAccessibilityConfig();
  const timeline = gsap.timeline();
  const transition = TEXT_TRANSITION_ANIMATIONS[type];

  if (accessibility.reduceMotion) {
    // Just update text immediately
    timeline.call(() => {
      element.textContent = newText;
    });
    return timeline;
  }

  // Exit animation
  timeline.to(element, transition.exit);

  // Update text
  timeline.call(() => {
    element.textContent = newText;
  });

  // Enter animation
  timeline.to(element, transition.enter);

  // Cleanup animation (if exists for blur type)
  if (type === 'blur' && 'cleanup' in transition) {
    timeline.to(element, (transition as any).cleanup, "+=0.5");
  }

  return timeline;
}

/**
 * Creates typewriter effect
 */
export function createTypewriterEffect(
  element: HTMLElement,
  text: string,
  config: { speed?: number; cursor?: boolean } = {}
): any {
  const { speed = 50, cursor = true } = config;
  const accessibility = getAccessibilityConfig();
  const timeline = gsap.timeline();
  
  if (accessibility.reduceMotion) {
    // Just set the text immediately
    timeline.call(() => {
      element.textContent = text;
    });
    return timeline;
  }
  
  // Clear initial text
  element.textContent = '';
  
  // Add cursor if enabled
  if (cursor) {
    element.textContent = '|';
  }
  
  // Animate each character
  text.split('').forEach((char, index) => {
    timeline.call(() => {
      const currentText = text.substring(0, index + 1);
      element.textContent = cursor ? currentText + '|' : currentText;
    }, [], index * (speed / 1000));
  });
  
  // Remove cursor at the end
  if (cursor) {
    timeline.call(() => {
      element.textContent = text;
    }, [], text.length * (speed / 1000) + 0.5);
  }
  
  return timeline;
}

/**
 * Gets animation configuration for letter type
 */
export function getAnimationConfigForType(type: string): LetterAnimationConfig {
  return LETTER_ANIMATION_CONFIGS[type] || LETTER_ANIMATION_CONFIGS.glow;
}
