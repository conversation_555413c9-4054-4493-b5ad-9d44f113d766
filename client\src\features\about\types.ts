/**
 * About Feature Types and Interfaces
 * Centralized type definitions for the about feature module
 */

export interface Skill {
  id: string;
  name: string;
  category: 'frontend' | 'backend' | 'ai-ml' | 'tools' | 'soft-skills';
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  experience: string; // e.g., "2 years", "5+ years"
  icon?: string;
  description?: string;
  projects?: string[]; // Project IDs where this skill was used
}

export interface PersonalInfo {
  name: string;
  title: string;
  location: string;
  email: string;
  bio: string;
  avatar?: string;
  socialLinks: {
    github?: string;
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
  interests: string[];
  languages: string[];
}

export interface AboutAnimationConfig {
  duration: number;
  ease: string;
  stagger: number;
  reduceMotion: boolean;
}

export interface ScannerAnimationProps {
  isActive: boolean;
  scanLines: number;
  speed: number;
  color: string;
  onScanComplete?: () => void;
}

export interface AIBrainOutlineProps {
  isAnimating: boolean;
  pulseSpeed: number;
  glowIntensity: number;
  className?: string;
}

export interface TechnicalSkillsProps {
  skills: Skill[];
  showCategories?: boolean;
  showLevels?: boolean;
  animateOnScroll?: boolean;
  className?: string;
}

export interface PersonalInfoProps {
  info: PersonalInfo;
  showAvatar?: boolean;
  showSocialLinks?: boolean;
  className?: string;
}

export interface AboutSectionProps {
  personalInfo: PersonalInfo;
  skills: Skill[];
  showAnimations?: boolean;
  className?: string;
}

export interface AnimatedContentProps {
  children: React.ReactNode;
  animationType: 'fade' | 'slide' | 'scale' | 'rotate';
  delay?: number;
  duration?: number;
  className?: string;
}

// Animation-related types
export interface AboutScrollTrigger {
  trigger: string;
  start: string;
  end: string;
  scrub?: boolean;
  pin?: boolean;
}

export interface SkillAnimationConfig {
  type: 'progress-bar' | 'circular' | 'pulse' | 'glow';
  duration: number;
  delay: number;
  easing: string;
}

// State management types
export interface AboutState {
  personalInfo: PersonalInfo;
  skills: Skill[];
  isLoading: boolean;
  error: string | null;
  activeSection: string | null;
  animationsEnabled: boolean;
}

export interface AboutActions {
  setPersonalInfo: (info: PersonalInfo) => void;
  setSkills: (skills: Skill[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setActiveSection: (section: string | null) => void;
  toggleAnimations: () => void;
  addSkill: (skill: Skill) => void;
  updateSkill: (id: string, updates: Partial<Skill>) => void;
  removeSkill: (id: string) => void;
}

export type AboutHookReturn = AboutState & AboutActions;

// Filter and sort types
export type SkillFilter = {
  category?: Skill['category'][];
  level?: Skill['level'][];
  searchQuery?: string;
};

export type SkillSortOption = {
  key: keyof Skill;
  direction: 'asc' | 'desc';
};

// Analytics types
export interface AboutAnalytics {
  sectionViews: Record<string, number>;
  skillInteractions: Record<string, number>;
  averageTimeOnSection: number;
  mostViewedSkills: string[];
}

// Accessibility types
export interface AboutAccessibilityConfig {
  announceSkillLevels: boolean;
  provideSectionSummaries: boolean;
  enableKeyboardNavigation: boolean;
  respectReducedMotion: boolean;
}

// Content types
export interface AboutContent {
  sections: {
    intro: {
      title: string;
      subtitle: string;
      description: string;
    };
    skills: {
      title: string;
      subtitle: string;
      categories: Record<Skill['category'], string>;
    };
    personal: {
      title: string;
      subtitle: string;
    };
  };
  metadata: {
    lastUpdated: Date;
    version: string;
  };
}
