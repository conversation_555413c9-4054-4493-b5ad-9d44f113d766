/**
 * Validation Service Tests
 * Tests for the centralized validation service
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { validationService } from '@/services/validationService';

describe('ValidationService', () => {
  describe('validateField', () => {
    it('should validate required fields', () => {
      const rule = { required: true };
      
      expect(validationService.validateField('', rule, 'Name')).toBe('Name is required');
      expect(validationService.validateField(null, rule, 'Name')).toBe('Name is required');
      expect(validationService.validateField(undefined, rule, 'Name')).toBe('Name is required');
      expect(validationService.validateField('John', rule, 'Name')).toBeNull();
    });

    it('should validate minimum length', () => {
      const rule = { minLength: 3 };
      
      expect(validationService.validateField('ab', rule, 'Name')).toBe('Name must be at least 3 characters');
      expect(validationService.validateField('abc', rule, 'Name')).toBeNull();
      expect(validationService.validateField('abcd', rule, 'Name')).toBeNull();
    });

    it('should validate maximum length', () => {
      const rule = { maxLength: 5 };
      
      expect(validationService.validateField('abcdef', rule, 'Name')).toBe('Name must be no more than 5 characters');
      expect(validationService.validateField('abcde', rule, 'Name')).toBeNull();
      expect(validationService.validateField('abc', rule, 'Name')).toBeNull();
    });

    it('should validate patterns', () => {
      const rule = { pattern: /^[a-zA-Z]+$/ };
      
      expect(validationService.validateField('abc123', rule, 'Name')).toBe('Name format is invalid');
      expect(validationService.validateField('abc', rule, 'Name')).toBeNull();
    });

    it('should use custom validation', () => {
      const rule = {
        custom: (value: string) => value === 'forbidden' ? 'This value is not allowed' : null
      };
      
      expect(validationService.validateField('forbidden', rule, 'Value')).toBe('This value is not allowed');
      expect(validationService.validateField('allowed', rule, 'Value')).toBeNull();
    });

    it('should use custom error messages', () => {
      const rule = { required: true, message: 'Custom required message' };
      
      expect(validationService.validateField('', rule, 'Name')).toBe('Custom required message');
    });
  });

  describe('validate', () => {
    const schema = {
      name: { required: true, minLength: 2 },
      email: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
      age: { custom: (value: number) => value < 18 ? 'Must be 18 or older' : null },
    };

    it('should validate valid data', () => {
      const data = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 25,
      };

      const result = validationService.validate(data, schema);

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
      expect(result.firstError).toBeUndefined();
    });

    it('should return errors for invalid data', () => {
      const data = {
        name: 'J',
        email: 'invalid-email',
        age: 16,
      };

      const result = validationService.validate(data, schema);

      expect(result.isValid).toBe(false);
      expect(result.errors.name).toBe('name must be at least 2 characters');
      expect(result.errors.email).toBe('email format is invalid');
      expect(result.errors.age).toBe('Must be 18 or older');
      expect(result.firstError).toBeDefined();
    });

    it('should trim values when option is enabled', () => {
      const data = {
        name: '  John Doe  ',
        email: '  <EMAIL>  ',
        age: 25,
      };

      const result = validationService.validate(data, schema, { trimValues: true });

      expect(result.isValid).toBe(true);
    });

    it('should stop on first error when option is enabled', () => {
      const data = {
        name: '',
        email: 'invalid',
        age: 16,
      };

      const result = validationService.validate(data, schema, { stopOnFirstError: true });

      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors)).toHaveLength(1);
    });
  });

  describe('createRules', () => {
    it('should create required rule', () => {
      const rule = validationService.createRules.required('Custom message');
      
      expect(rule.required).toBe(true);
      expect(rule.message).toBe('Custom message');
    });

    it('should create email rule', () => {
      const rule = validationService.createRules.email();
      
      expect(rule.pattern).toBeDefined();
      expect(rule.message).toContain('email');
    });

    it('should create length rules', () => {
      const minRule = validationService.createRules.minLength(5);
      const maxRule = validationService.createRules.maxLength(10);
      const lengthRule = validationService.createRules.length(5, 10);
      
      expect(minRule.minLength).toBe(5);
      expect(maxRule.maxLength).toBe(10);
      expect(lengthRule.minLength).toBe(5);
      expect(lengthRule.maxLength).toBe(10);
    });

    it('should create custom rule', () => {
      const validator = (value: string) => value === 'test' ? null : 'Invalid';
      const rule = validationService.createRules.custom(validator);
      
      expect(rule.custom).toBe(validator);
    });

    it('should create oneOf rule', () => {
      const rule = validationService.createRules.oneOf(['a', 'b', 'c']);
      
      expect(rule.custom).toBeDefined();
      expect(rule.custom!('a')).toBeNull();
      expect(rule.custom!('d')).toContain('must be one of');
    });
  });

  describe('schemas', () => {
    it('should validate contact form data', () => {
      const validData = {
        name: 'John Doe',
        email: '<EMAIL>',
        message: 'This is a test message with enough characters.',
      };

      const result = validationService.validate(validData, validationService.schemas.contact);
      expect(result.isValid).toBe(true);
    });

    it('should validate user registration data', () => {
      const validData = {
        username: 'johndoe123',
        email: '<EMAIL>',
        password: 'StrongPass123!',
      };

      const result = validationService.validate(validData, validationService.schemas.user);
      expect(result.isValid).toBe(true);
    });

    it('should validate project data', () => {
      const validData = {
        title: 'My Project',
        description: 'This is a detailed description of my project.',
        type: 'web',
        technologies: ['React', 'TypeScript'],
      };

      const result = validationService.validate(validData, validationService.schemas.project);
      expect(result.isValid).toBe(true);
    });
  });

  describe('sanitize', () => {
    it('should trim whitespace', () => {
      const data = {
        name: '  John Doe  ',
        email: '  <EMAIL>  ',
      };

      const result = validationService.sanitize(data);

      expect(result.name).toBe('John Doe');
      expect(result.email).toBe('<EMAIL>');
    });

    it('should remove HTML tags', () => {
      const data = {
        name: '<script>alert("xss")</script>John',
        message: '<b>Bold</b> text with <i>italic</i>',
      };

      const result = validationService.sanitize(data);

      expect(result.name).toBe('John');
      expect(result.message).toBe('Bold text with italic');
    });

    it('should escape special characters', () => {
      const data = {
        message: 'Test & "quotes" <script>',
      };

      const result = validationService.sanitize(data);

      expect(result.message).toBe('Test &amp; &quot;quotes&quot; ');
    });
  });

  describe('validateEmailDomain', () => {
    it('should validate valid email domains', async () => {
      expect(await validationService.validateEmailDomain('<EMAIL>')).toBe(true);
      expect(await validationService.validateEmailDomain('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email domains', async () => {
      expect(await validationService.validateEmailDomain('test@')).toBe(false);
      expect(await validationService.validateEmailDomain('test@invalid')).toBe(false);
      expect(await validationService.validateEmailDomain('invalid-email')).toBe(false);
    });
  });

  describe('validateFile', () => {
    const createMockFile = (name: string, size: number, type: string): File => {
      return new File(['content'], name, { type }) as File & { size: number };
    };

    it('should validate file size', () => {
      const file = createMockFile('test.jpg', 1024 * 1024, 'image/jpeg'); // 1MB
      file.size = 1024 * 1024;

      const result = validationService.validateFile(file, { maxSize: 2 * 1024 * 1024 }); // 2MB limit

      expect(result.isValid).toBe(true);
    });

    it('should reject oversized files', () => {
      const file = createMockFile('test.jpg', 3 * 1024 * 1024, 'image/jpeg'); // 3MB
      file.size = 3 * 1024 * 1024;

      const result = validationService.validateFile(file, { maxSize: 2 * 1024 * 1024 }); // 2MB limit

      expect(result.isValid).toBe(false);
      expect(result.errors.size).toContain('File size must be less than');
    });

    it('should validate file types', () => {
      const file = createMockFile('test.jpg', 1024, 'image/jpeg');

      const result = validationService.validateFile(file, { allowedTypes: ['image/jpeg', 'image/png'] });

      expect(result.isValid).toBe(true);
    });

    it('should reject invalid file types', () => {
      const file = createMockFile('test.exe', 1024, 'application/exe');

      const result = validationService.validateFile(file, { allowedTypes: ['image/jpeg', 'image/png'] });

      expect(result.isValid).toBe(false);
      expect(result.errors.type).toContain('File type');
    });

    it('should validate file extensions', () => {
      const file = createMockFile('test.jpg', 1024, 'image/jpeg');

      const result = validationService.validateFile(file, { allowedExtensions: ['jpg', 'png'] });

      expect(result.isValid).toBe(true);
    });

    it('should reject invalid file extensions', () => {
      const file = createMockFile('test.exe', 1024, 'application/exe');

      const result = validationService.validateFile(file, { allowedExtensions: ['jpg', 'png'] });

      expect(result.isValid).toBe(false);
      expect(result.errors.extension).toContain('File extension');
    });
  });
});
