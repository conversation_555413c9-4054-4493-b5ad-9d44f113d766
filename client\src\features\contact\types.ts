/**
 * Contact Feature Types and Interfaces
 * Centralized type definitions for the contact feature module
 */

export interface ContactFormData {
  name: string;
  email: string;
  message: string;
  subject?: string;
  phone?: string;
  company?: string;
}

export interface ContactFormErrors {
  name?: string;
  email?: string;
  message?: string;
  subject?: string;
  phone?: string;
  company?: string;
  general?: string;
}

export interface ContactFormState {
  data: ContactFormData;
  errors: ContactFormErrors;
  isSubmitting: boolean;
  isSubmitted: boolean;
  submitError: string | null;
  submitSuccess: boolean;
}

export interface ContactFormActions {
  updateField: (field: keyof ContactFormData, value: string) => void;
  setError: (field: keyof ContactFormErrors, error: string) => void;
  clearError: (field: keyof ContactFormErrors) => void;
  clearAllErrors: () => void;
  setSubmitting: (isSubmitting: boolean) => void;
  setSubmitted: (isSubmitted: boolean) => void;
  setSubmitError: (error: string | null) => void;
  setSubmitSuccess: (success: boolean) => void;
  resetForm: () => void;
  submitForm: () => Promise<void>;
}

export type ContactFormHookReturn = ContactFormState & ContactFormActions;

export interface ContactValidationRules {
  name: {
    required: boolean;
    minLength: number;
    maxLength: number;
  };
  email: {
    required: boolean;
    pattern: RegExp;
  };
  message: {
    required: boolean;
    minLength: number;
    maxLength: number;
  };
  phone?: {
    required: boolean;
    pattern: RegExp;
  };
}

export interface ContactAnimationConfig {
  duration: number;
  ease: string;
  stagger: number;
  reduceMotion: boolean;
}

export interface SpaceshipAnimationProps {
  direction: 'left' | 'right';
  delay?: number;
  duration?: number;
  onComplete?: () => void;
}

export interface ContactInvitationProps {
  title?: string;
  subtitle?: string;
  description?: string;
  className?: string;
}

export interface ContactFormProps {
  onSubmit?: (data: ContactFormData) => Promise<void>;
  initialData?: Partial<ContactFormData>;
  validationRules?: Partial<ContactValidationRules>;
  className?: string;
}

export interface SpaceshipHeaderProps {
  title: string;
  subtitle?: string;
  showAnimation?: boolean;
  className?: string;
}

// Email service types
export interface EmailServiceConfig {
  serviceId: string;
  templateId: string;
  publicKey: string;
}

export interface EmailTemplateData {
  from_name: string;
  from_email: string;
  message: string;
  subject?: string;
  to_email: string;
}

export interface EmailServiceResponse {
  success: boolean;
  message: string;
  error?: string;
}

// Analytics types
export interface ContactAnalytics {
  formViews: number;
  formSubmissions: number;
  conversionRate: number;
  averageTimeToSubmit: number;
  mostCommonErrors: string[];
}

export interface ContactEvent {
  type: 'form_view' | 'form_submit' | 'form_error' | 'email_sent';
  timestamp: Date;
  data?: any;
}

// Accessibility types
export interface ContactAccessibilityConfig {
  announceErrors: boolean;
  announceSuccess: boolean;
  focusManagement: boolean;
  keyboardNavigation: boolean;
}

// Animation event types
export interface ContactAnimationEvents {
  onFormEnter: () => void;
  onFormExit: () => void;
  onSpaceshipFlyIn: () => void;
  onTextReveal: () => void;
  onSubmitAnimation: () => void;
}
