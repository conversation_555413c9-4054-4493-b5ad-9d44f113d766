/**
 * Contact Animations Hook
 * Specialized GSAP animations for contact components
 */

import { useRef, useCallback } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { getAccessibilityConfig } from '@/utils';
import { ContactAnimationConfig, SpaceshipAnimationProps } from '../types';

export const useContactAnimations = () => {
  const { gsap, isAvailable } = useGSAP();
  const timelineRef = useRef<any>(null);
  const { reduceMotion } = getAccessibilityConfig();

  const defaultConfig: ContactAnimationConfig = {
    duration: 0.8,
    ease: 'power2.out',
    stagger: 0.1,
    reduceMotion,
  };

  const animateContactSection = useCallback((
    sectionRef: React.RefObject<HTMLElement>,
    config: Partial<ContactAnimationConfig> = {}
  ) => {
    if (!isAvailable || !sectionRef.current || reduceMotion) return;

    const finalConfig = { ...defaultConfig, ...config };
    const elements = sectionRef.current.querySelectorAll('[data-animate]');

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline();

    timelineRef.current
      .set(elements, {
        opacity: 0,
        y: 50,
      })
      .to(elements, {
        opacity: 1,
        y: 0,
        duration: finalConfig.duration,
        ease: finalConfig.ease,
        stagger: finalConfig.stagger,
      });

    return timelineRef.current;
  }, [gsap, isAvailable, reduceMotion]);

  const animateSpaceshipFlyIn = useCallback((
    spaceshipRef: React.RefObject<HTMLElement>,
    props: SpaceshipAnimationProps
  ) => {
    if (!isAvailable || !spaceshipRef.current || reduceMotion) {
      if (spaceshipRef.current) {
        spaceshipRef.current.style.opacity = '1';
        spaceshipRef.current.style.transform = 'translateX(0)';
      }
      return;
    }

    const startX = props.direction === 'left' ? -200 : 200;
    const { delay = 0, duration = 1.5, onComplete } = props;

    gsap.set(spaceshipRef.current, {
      opacity: 0,
      x: startX,
      rotation: props.direction === 'left' ? -15 : 15,
    });

    return gsap.to(spaceshipRef.current, {
      opacity: 1,
      x: 0,
      rotation: 0,
      duration,
      ease: 'power2.out',
      delay,
      onComplete,
    });
  }, [gsap, isAvailable, reduceMotion]);

  const animateTextReveal = useCallback((
    textRef: React.RefObject<HTMLElement>,
    isAlienText: boolean = false
  ) => {
    if (!isAvailable || !textRef.current || reduceMotion) {
      if (textRef.current) {
        textRef.current.style.opacity = '1';
      }
      return;
    }

    if (isAlienText) {
      // Alien/cryptic text animation
      const chars = textRef.current.querySelectorAll('.char');
      
      gsap.set(chars, {
        opacity: 0,
        scale: 0.5,
        rotation: () => Math.random() * 360,
      });

      return gsap.to(chars, {
        opacity: 1,
        scale: 1,
        rotation: 0,
        duration: 0.1,
        ease: 'power2.out',
        stagger: 0.05,
      });
    } else {
      // Normal text reveal
      gsap.set(textRef.current, {
        opacity: 0,
        y: 30,
      });

      return gsap.to(textRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: 'power2.out',
      });
    }
  }, [gsap, isAvailable, reduceMotion]);

  const animateFormSubmission = useCallback((
    formRef: React.RefObject<HTMLElement>,
    isSuccess: boolean
  ) => {
    if (!isAvailable || !formRef.current || reduceMotion) return;

    const submitButton = formRef.current.querySelector('[type="submit"]');
    
    if (isSuccess) {
      return gsap.to(submitButton, {
        scale: 1.1,
        backgroundColor: '#10B981',
        duration: 0.3,
        ease: 'power2.out',
        yoyo: true,
        repeat: 1,
      });
    } else {
      return gsap.to(submitButton, {
        x: [-10, 10, -10, 10, 0],
        duration: 0.5,
        ease: 'power2.out',
      });
    }
  }, [gsap, isAvailable, reduceMotion]);

  const animateParticleExplosion = useCallback((
    containerRef: React.RefObject<HTMLElement>,
    centerX: number,
    centerY: number
  ) => {
    if (!isAvailable || !containerRef.current || reduceMotion) return;

    const particles: HTMLElement[] = [];
    const particleCount = 20;

    // Create particles
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-2 h-2 bg-blue-400 rounded-full pointer-events-none';
      particle.style.left = `${centerX}px`;
      particle.style.top = `${centerY}px`;
      containerRef.current.appendChild(particle);
      particles.push(particle);
    }

    // Animate particles
    const tl = gsap.timeline({
      onComplete: () => {
        // Clean up particles
        particles.forEach(particle => {
          if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
          }
        });
      },
    });

    particles.forEach((particle, index) => {
      const angle = (index / particleCount) * Math.PI * 2;
      const distance = 100 + Math.random() * 50;
      const x = Math.cos(angle) * distance;
      const y = Math.sin(angle) * distance;

      tl.to(particle, {
        x,
        y,
        opacity: 0,
        scale: 0,
        duration: 1,
        ease: 'power2.out',
      }, 0);
    });

    return tl;
  }, [gsap, isAvailable, reduceMotion]);

  const cleanup = useCallback(() => {
    if (timelineRef.current) {
      timelineRef.current.kill();
      timelineRef.current = null;
    }
  }, []);

  return {
    animateContactSection,
    animateSpaceshipFlyIn,
    animateTextReveal,
    animateFormSubmission,
    animateParticleExplosion,
    cleanup,
    isAvailable,
  };
};

export default useContactAnimations;
