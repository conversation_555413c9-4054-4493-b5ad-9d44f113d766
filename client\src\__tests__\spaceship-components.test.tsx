/**
 * Tests for individual spaceship components
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import RocketSpaceship from '@/components/RocketSpaceship';
import UFOSpaceship from '@/components/UFOSpaceship';

// Mock GSAP and related hooks
vi.mock('@/hooks/useGSAP', () => ({
  useGSAP: () => ({
    gsap: {
      timeline: () => ({
        set: vi.fn().mockReturnThis(),
        to: vi.fn().mockReturnThis(),
        fromTo: vi.fn().mockReturnThis(),
        add: vi.fn().mockReturnThis(),
      }),
      set: vi.fn(),
    },
    animate: vi.fn(),
    isAvailable: true,
  }),
  useSpaceshipAnimation: () => ({
    createFlightAnimation: vi.fn(),
    createHoverEffect: vi.fn(() => ({ enter: vi.fn(), leave: vi.fn() })),
    createFloatingAnimation: vi.fn(),
    createLaserAnimation: vi.fn(),
    createSpaceshipSequence: vi.fn(),
    isAvailable: true,
  }),
  useScrollTrigger: () => ({
    createScrollTrigger: vi.fn(),
    isAvailable: true,
  }),
}));

vi.mock('@/components/ErrorBoundary', () => ({
  useErrorHandler: () => ({
    handleError: vi.fn(),
  }),
}));

vi.mock('@/utils', () => ({
  getAccessibilityConfig: () => ({
    reduceMotion: false,
  }),
}));

describe('RocketSpaceship', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders rocket SVG with correct structure', () => {
    const { container } = render(<RocketSpaceship />);

    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();

    // Check for rocket-specific elements (UFO-style saucer shape)
    const saucer = container.querySelector('ellipse');
    expect(saucer).toBeInTheDocument();

    const fins = container.querySelector('polygon');
    expect(fins).toBeInTheDocument();
  });

  it('handles click events', () => {
    const onClick = vi.fn();
    const { container } = render(<RocketSpaceship onClick={onClick} />);

    const rocketContainer = container.firstChild as HTMLElement;

    fireEvent.click(rocketContainer);
    expect(onClick).toHaveBeenCalled();
  });

  it('applies correct size classes', () => {
    const { container } = render(<RocketSpaceship size="lg" />);

    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '88');
    expect(svg).toHaveAttribute('height', '72');
  });

  it('includes laser beam elements', () => {
    const { container } = render(<RocketSpaceship />);

    // Check for laser beam gradients and elements
    const laserGradient = container.querySelector('#laser-beam');
    expect(laserGradient).toBeInTheDocument();

    // Laser beam is implemented as a polygon, not lines
    const laserBeam = container.querySelector('polygon[fill="url(#laser-beam)"]');
    expect(laserBeam).toBeInTheDocument();
  });

  it('includes exhaust ports', () => {
    const { container } = render(<RocketSpaceship />);

    // Check for exhaust port circles
    const exhaustPorts = container.querySelectorAll('circle');
    expect(exhaustPorts.length).toBeGreaterThan(0);
  });
});

describe('UFOSpaceship', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders UFO SVG with correct structure', () => {
    const { container } = render(<UFOSpaceship />);
    
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    
    // Check for UFO-specific elements
    const saucer = container.querySelector('ellipse');
    expect(saucer).toBeInTheDocument();
    
    const tractorBeam = container.querySelector('polygon');
    expect(tractorBeam).toBeInTheDocument();
  });

  it('handles hover events', () => {
    const onHover = vi.fn();
    const { container } = render(<UFOSpaceship onHover={onHover} />);
    
    const ufoContainer = container.firstChild as HTMLElement;
    
    fireEvent.mouseEnter(ufoContainer);
    expect(onHover).toHaveBeenCalledWith(true);
    
    fireEvent.mouseLeave(ufoContainer);
    expect(onHover).toHaveBeenCalledWith(false);
  });

  it('applies correct size classes', () => {
    const { container } = render(<UFOSpaceship size="lg" />);
    
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '88');
    expect(svg).toHaveAttribute('height', '72');
  });

  it('includes blinking lights', () => {
    const { container } = render(<UFOSpaceship />);
    
    // Check for multiple light circles
    const lights = container.querySelectorAll('circle');
    expect(lights.length).toBeGreaterThan(0);
  });

  it('includes tractor beam gradient', () => {
    const { container } = render(<UFOSpaceship />);
    
    const beamGradient = container.querySelector('#tractor-beam');
    expect(beamGradient).toBeInTheDocument();
  });
});
